#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste Simples - Identificar problema no cadastro
Sistema: RLPONTO-WEB
Data: 20/07/2025
"""

import requests
import json
from datetime import datetime

# Configurações
BASE_URL = "http://10.19.208.31:5000"
LOGIN_URL = f"{BASE_URL}/login"
CADASTRO_URL = f"{BASE_URL}/funcionarios/cadastrar"

def fazer_login():
    """Fazer login no sistema"""
    print("🔐 Fazendo login...")
    
    session = requests.Session()
    
    # Fazer login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code in [302, 200]:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print(f"❌ Erro no login: {response.status_code}")
        return None

def obter_formulario(session):
    """Obter formulário de cadastro para ver campos necessários"""
    print("📋 Obtendo formulário de cadastro...")
    
    response = session.get(CADASTRO_URL)
    
    if response.status_code == 200:
        content = response.text
        
        # Procurar por campos obrigatórios
        import re
        required_fields = re.findall(r'name="([^"]+)"[^>]*required', content)
        print(f"📝 Campos obrigatórios encontrados: {required_fields}")
        
        # Procurar por opções de empresa
        empresa_options = re.findall(r'<option value="(\d+)"[^>]*>([^<]+)</option>', content)
        if empresa_options:
            print(f"🏢 Empresas disponíveis:")
            for value, name in empresa_options:
                print(f"   ID {value}: {name}")
        
        return True
    else:
        print(f"❌ Erro ao obter formulário: {response.status_code}")
        return False

def testar_cadastro_minimo(session):
    """Testar cadastro com dados mínimos obrigatórios"""
    print("🧪 Testando cadastro com dados mínimos...")
    
    timestamp = datetime.now().strftime("%H%M%S")
    
    # Dados mínimos obrigatórios
    dados_minimos = {
        'nome_completo': f'TESTE MINIMO {timestamp}',
        'cpf': '000.000.000-00',
        'rg': f'MIN-{timestamp}',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'endereco_cep': '00000-000',
        'endereco_estado': 'SP',
        'telefone1': '(11) 99999-9999',
        'cargo': 'TESTE',
        'setor_obra': 'TESTE',
        'matricula_empresa': f'MIN{timestamp}',
        'data_admissao': datetime.now().strftime('%Y-%m-%d'),
        'tipo_contrato': 'CLT',
        'nivel_acesso': 'Funcionario',
        'status_cadastro': 'Ativo',
        'empresa_id': '4',  # Usar empresa válida encontrada
        'horas_semanais_obrigatorias': '44.00'  # Campo obrigatório encontrado
    }
    
    print(f"📝 Enviando dados mínimos:")
    for key, value in dados_minimos.items():
        print(f"   {key}: {value}")
    
    response = session.post(CADASTRO_URL, data=dados_minimos, allow_redirects=False)
    
    print(f"📤 Status da resposta: {response.status_code}")
    
    if response.status_code == 302:
        location = response.headers.get('Location', '')
        print(f"✅ Redirect para: {location}")
        return True
    else:
        print(f"❌ Erro no cadastro")
        
        # Procurar por mensagens de erro específicas
        content = response.text
        
        # Procurar por alerts de erro
        import re
        error_patterns = [
            r'<div[^>]*alert-danger[^>]*>(.*?)</div>',
            r'<li[^>]*>(.*?)</li>',
            r'class="error[^"]*"[^>]*>(.*?)</',
        ]
        
        errors_found = []
        for pattern in error_patterns:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                clean_error = re.sub(r'<[^>]+>', '', match).strip()
                if clean_error and len(clean_error) > 5:  # Filtrar textos muito pequenos
                    errors_found.append(clean_error)
        
        if errors_found:
            print("🔍 Erros encontrados:")
            for error in errors_found[:5]:  # Mostrar apenas os primeiros 5
                print(f"   ❌ {error}")
        else:
            print("🔍 Nenhum erro específico encontrado na página")
            # Mostrar parte do conteúdo para debug
            print(f"📄 Início do conteúdo: {content[:300]}")
        
        return False

def main():
    """Função principal"""
    print("🧪 TESTE SIMPLES: IDENTIFICAR PROBLEMA NO CADASTRO")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Fazer login
    session = fazer_login()
    if not session:
        print("❌ Falha no login - teste abortado")
        return
    
    # Obter formulário
    if not obter_formulario(session):
        print("❌ Falha ao obter formulário - teste abortado")
        return
    
    print()
    
    # Testar cadastro
    sucesso = testar_cadastro_minimo(session)
    
    print("\n" + "=" * 60)
    print("📊 RESULTADO DO TESTE:")
    
    if sucesso:
        print("✅ SUCESSO: Cadastro funcionou corretamente")
    else:
        print("❌ FALHA: Problema identificado no cadastro")
    
    print("\n✅ TESTE CONCLUÍDO")

if __name__ == "__main__":
    main()
