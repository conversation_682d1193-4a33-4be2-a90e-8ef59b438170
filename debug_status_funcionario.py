#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug <PERSON>ript - Investigar problema de status_cadastro em funcionários
Sistema: RLPONTO-WEB
Data: 20/07/2025
"""

import sys
import os
import pymysql
from datetime import datetime

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def conectar_banco():
    """Conectar ao banco de dados"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Conexão com banco estabelecida")
        return connection
    except Exception as e:
        print(f"❌ Erro ao conectar: {e}")
        return None

def verificar_estrutura_tabela():
    """Verificar estrutura da tabela funcionários"""
    print("\n🔍 VERIFICANDO ESTRUTURA DA TABELA FUNCIONÁRIOS")
    print("=" * 60)
    
    conn = conectar_banco()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # Verificar estrutura do campo status_cadastro
        cursor.execute("DESCRIBE funcionarios")
        campos = cursor.fetchall()
        
        for campo in campos:
            if 'status' in campo[0].lower():
                print(f"📋 Campo: {campo[0]}")
                print(f"   Tipo: {campo[1]}")
                print(f"   Null: {campo[2]}")
                print(f"   Key: {campo[3]}")
                print(f"   Default: {campo[4]}")
                print(f"   Extra: {campo[5]}")
                print()
        
    except Exception as e:
        print(f"❌ Erro: {e}")
    finally:
        conn.close()

def verificar_funcionarios_recentes():
    """Verificar funcionários cadastrados recentemente"""
    print("\n🔍 VERIFICANDO FUNCIONÁRIOS RECENTES")
    print("=" * 60)
    
    conn = conectar_banco()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Buscar últimos 10 funcionários cadastrados
        sql = """
        SELECT id, nome_completo, status_cadastro, data_cadastro, data_atualizacao
        FROM funcionarios 
        ORDER BY data_cadastro DESC 
        LIMIT 10
        """
        
        cursor.execute(sql)
        funcionarios = cursor.fetchall()
        
        print(f"📊 Encontrados {len(funcionarios)} funcionários recentes:")
        print()
        
        for func in funcionarios:
            print(f"ID: {func['id']}")
            print(f"Nome: {func['nome_completo']}")
            print(f"Status: {func['status_cadastro']}")
            print(f"Cadastro: {func['data_cadastro']}")
            print(f"Atualização: {func['data_atualizacao']}")
            print("-" * 40)
        
    except Exception as e:
        print(f"❌ Erro: {e}")
    finally:
        conn.close()

def verificar_triggers():
    """Verificar triggers que podem afetar funcionários"""
    print("\n🔍 VERIFICANDO TRIGGERS NA TABELA FUNCIONÁRIOS")
    print("=" * 60)
    
    conn = conectar_banco()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Verificar triggers
        sql = """
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_TIMING
        FROM information_schema.TRIGGERS 
        WHERE EVENT_OBJECT_TABLE = 'funcionarios'
        AND TRIGGER_SCHEMA = 'controle_ponto'
        """
        
        cursor.execute(sql)
        triggers = cursor.fetchall()
        
        if triggers:
            print(f"📋 Encontrados {len(triggers)} triggers:")
            for trigger in triggers:
                print(f"- {trigger['TRIGGER_NAME']} ({trigger['ACTION_TIMING']} {trigger['EVENT_MANIPULATION']})")
        else:
            print("✅ Nenhum trigger encontrado na tabela funcionários")
        
    except Exception as e:
        print(f"❌ Erro: {e}")
    finally:
        conn.close()

def simular_insercao():
    """Simular inserção de funcionário para debug"""
    print("\n🧪 SIMULANDO INSERÇÃO DE FUNCIONÁRIO")
    print("=" * 60)
    
    conn = conectar_banco()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Dados de teste
        dados_teste = {
            'nome_completo': 'TESTE DEBUG STATUS',
            'cpf': '000.000.000-00',
            'rg': 'DEBUG-TEST',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'Solteiro',
            'nacionalidade': 'Brasileira',
            'pis_pasep': '000.00000.00-0',  # VALOR PADRÃO
            'endereco_cep': '00000-000',
            'endereco_estado': 'SP',
            'telefone1': '(00) 00000-0000',
            'cargo': 'TESTE',
            'setor_obra': 'DEBUG',
            'matricula_empresa': f'DEBUG{datetime.now().strftime("%H%M%S")}',
            'data_admissao': datetime.now().strftime('%Y-%m-%d'),
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'Funcionario',
            'status_cadastro': 'Ativo',  # EXPLICITAMENTE ATIVO
            'empresa_id': 1
        }
        
        print(f"📝 Dados para inserção:")
        print(f"   Nome: {dados_teste['nome_completo']}")
        print(f"   Status: {dados_teste['status_cadastro']}")
        print(f"   Empresa ID: {dados_teste['empresa_id']}")
        print()
        
        # Inserir funcionário de teste
        sql = """
        INSERT INTO funcionarios (
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            pis_pasep, endereco_cep, endereco_estado, telefone1,
            cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
            nivel_acesso, status_cadastro, empresa_id
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s
        )
        """

        params = (
            dados_teste['nome_completo'], dados_teste['cpf'], dados_teste['rg'],
            dados_teste['data_nascimento'], dados_teste['sexo'], dados_teste['estado_civil'],
            dados_teste['nacionalidade'], dados_teste['pis_pasep'], dados_teste['endereco_cep'],
            dados_teste['endereco_estado'], dados_teste['telefone1'], dados_teste['cargo'],
            dados_teste['setor_obra'], dados_teste['matricula_empresa'], dados_teste['data_admissao'],
            dados_teste['tipo_contrato'], dados_teste['nivel_acesso'], dados_teste['status_cadastro'],
            dados_teste['empresa_id']
        )
        
        cursor.execute(sql, params)
        funcionario_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Funcionário inserido com ID: {funcionario_id}")
        
        # Verificar imediatamente após inserção
        sql_verificar = "SELECT id, nome_completo, status_cadastro FROM funcionarios WHERE id = %s"
        cursor.execute(sql_verificar, (funcionario_id,))
        resultado = cursor.fetchone()
        
        if resultado:
            print(f"🔍 Verificação imediata:")
            print(f"   ID: {resultado['id']}")
            print(f"   Nome: {resultado['nome_completo']}")
            print(f"   Status: {resultado['status_cadastro']}")
            
            if resultado['status_cadastro'] == 'Ativo':
                print("✅ Status correto: Ativo")
            else:
                print(f"❌ Status incorreto: {resultado['status_cadastro']} (esperado: Ativo)")
        
        # Limpar dados de teste
        print(f"\n🧹 Removendo dados de teste...")
        cursor.execute("DELETE FROM funcionarios WHERE id = %s", (funcionario_id,))
        conn.commit()
        print("✅ Dados de teste removidos")
        
    except Exception as e:
        print(f"❌ Erro na simulação: {e}")
        conn.rollback()
    finally:
        conn.close()

def main():
    """Função principal"""
    print("🔍 DEBUG: INVESTIGAÇÃO DO PROBLEMA DE STATUS_CADASTRO")
    print("=" * 70)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Executar verificações
    verificar_estrutura_tabela()
    verificar_funcionarios_recentes()
    verificar_triggers()
    simular_insercao()
    
    print("\n✅ DEBUG CONCLUÍDO")

if __name__ == "__main__":
    main()
