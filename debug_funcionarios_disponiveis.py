#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== DEBUG FUNCIONÁRIOS DISPONÍVEIS ===\n")
        
        # 1. Verificar funcionário TESTE MOV 2
        print("1. 🔍 FUNCIONÁRIO TESTE MOV 2:")
        teste_mov = db.execute_query("""
            SELECT id, nome_completo, status_cadastro, empresa_id, ativo
            FROM funcionarios
            WHERE nome_completo LIKE %s
        """, ('%TESTE MOV%',))
        
        if teste_mov:
            for func in teste_mov:
                print(f"   ID: {func['id']}")
                print(f"   Nome: {func['nome_completo']}")
                print(f"   Status: {func['status_cadastro']}")
                print(f"   Empresa: {func['empresa_id']}")
                print(f"   Ativo: {func['ativo']}")
        else:
            print("   ❌ Funcionário TESTE MOV não encontrado")
        
        # 2. Verificar query de funcionários disponíveis
        print(f"\n2. 📋 QUERY FUNCIONÁRIOS DISPONÍVEIS:")
        
        # Esta é provavelmente a query usada na interface
        funcionarios_disponiveis = db.execute_query("""
            SELECT f.id, f.nome_completo, f.cargo, f.status_cadastro, f.empresa_id
            FROM funcionarios f
            WHERE f.status_cadastro = 'Ativo' 
            AND f.ativo = 1
            ORDER BY f.nome_completo
        """)
        
        print(f"   Total de funcionários disponíveis: {len(funcionarios_disponiveis)}")
        
        for func in funcionarios_disponiveis:
            if 'TESTE' in func['nome_completo'].upper():
                print(f"   🧪 TESTE: ID {func['id']} - {func['nome_completo']} (Empresa: {func['empresa_id']})")
        
        # 3. Verificar se há alocações ativas para TESTE MOV 2
        if teste_mov:
            teste_id = teste_mov[0]['id']
            print(f"\n3. 🔗 ALOCAÇÕES ATIVAS PARA TESTE MOV 2 (ID {teste_id}):")
            
            alocacoes = db.execute_query("""
                SELECT fa.id, fa.empresa_cliente_id, fa.ativo, fa.data_inicio, fa.data_fim,
                       e.razao_social as empresa_nome
                FROM funcionario_alocacoes fa
                JOIN empresas e ON fa.empresa_cliente_id = e.id
                WHERE fa.funcionario_id = %s AND fa.ativo = 1
            """, (teste_id,))
            
            if alocacoes:
                print(f"   ❌ Funcionário tem {len(alocacoes)} alocação(ões) ativa(s):")
                for aloc in alocacoes:
                    print(f"     - ID {aloc['id']}: {aloc['empresa_nome']} ({aloc['data_inicio']} até {aloc['data_fim'] or 'Em aberto'})")
                print("   Por isso ainda aparece na lista!")
            else:
                print(f"   ✅ Nenhuma alocação ativa encontrada")
        
        # 4. Verificar query que deveria filtrar funcionários já alocados
        print(f"\n4. 🚫 FUNCIONÁRIOS QUE DEVERIAM SER FILTRADOS:")
        
        # Query para funcionários que já estão alocados
        ja_alocados = db.execute_query("""
            SELECT DISTINCT f.id, f.nome_completo, fa.empresa_cliente_id, e.razao_social
            FROM funcionarios f
            JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            WHERE fa.ativo = 1 
            AND f.status_cadastro = 'Ativo'
            ORDER BY f.nome_completo
        """)
        
        print(f"   Funcionários já alocados: {len(ja_alocados)}")
        for aloc in ja_alocados:
            print(f"   - ID {aloc['id']}: {aloc['nome_completo']} → {aloc['razao_social']}")
        
        # 5. Query correta para funcionários disponíveis (não alocados)
        print(f"\n5. ✅ FUNCIONÁRIOS REALMENTE DISPONÍVEIS:")
        
        disponiveis_correto = db.execute_query("""
            SELECT f.id, f.nome_completo, f.cargo, f.empresa_id
            FROM funcionarios f
            WHERE f.status_cadastro = 'Ativo' 
            AND f.ativo = 1
            AND f.id NOT IN (
                SELECT DISTINCT fa.funcionario_id 
                FROM funcionario_alocacoes fa 
                WHERE fa.ativo = 1
            )
            ORDER BY f.nome_completo
        """)
        
        print(f"   Funcionários realmente disponíveis: {len(disponiveis_correto)}")
        for disp in disponiveis_correto:
            print(f"   - ID {disp['id']}: {disp['nome_completo']} (Empresa: {disp['empresa_id']})")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
