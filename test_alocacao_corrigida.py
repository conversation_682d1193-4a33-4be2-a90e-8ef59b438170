#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_alocacao():
    """Testar alocação com tratamento de erro corrigido"""
    
    print("=== TESTE ALOCAÇÃO CORRIGIDA ===\n")
    
    # Fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    login_response = session.post('http://************/login', data=login_data)
    
    print(f"1. Login status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ Falha no login")
        return
    
    # Dados de teste para alocação
    alocacao_data = {
        'funcionario_id': '35',  # Richardson
        'empresa_cliente_id': '11',  # AiNexus
        'jornada_trabalho_id': '3',  # Segundo Turno
        'cargo_no_cliente': 'TESTE CORREÇÃO',
        'data_inicio': '2025-07-21',
        'percentual_alocacao': '100',
        'observacoes': 'Teste de correção do bug'
    }
    
    print(f"2. Testando alocação...")
    print(f"   Funcionário: {alocacao_data['funcionario_id']}")
    print(f"   Cliente: {alocacao_data['empresa_cliente_id']}")
    print(f"   Jornada: {alocacao_data['jornada_trabalho_id']}")
    
    # Testar alocação
    alocacao_response = session.post(
        'http://************/empresa-principal/funcionarios/alocar', 
        data=alocacao_data
    )
    
    print(f"\n3. Resultado da alocação:")
    print(f"   Status HTTP: {alocacao_response.status_code}")
    
    try:
        response_data = alocacao_response.json()
        print(f"   Success: {response_data.get('success')}")
        print(f"   Message: {response_data.get('message')}")
        
        if response_data.get('success'):
            print("   ✅ Alocação bem-sucedida!")
        else:
            print("   ❌ Alocação falhou (como esperado se houver erro)")
            
    except json.JSONDecodeError:
        print(f"   ❌ Resposta não é JSON válido: {alocacao_response.text[:200]}")
    
    # Verificar se a alocação foi realmente criada no banco
    print(f"\n4. Verificando no banco de dados...")
    
    # Fazer uma nova requisição para verificar contagem
    clientes_response = session.get('http://************/empresa-principal/clientes')
    
    if clientes_response.status_code == 200:
        print("   ✅ Página de clientes carregada")
        # Aqui você pode verificar se a contagem mudou
    else:
        print(f"   ❌ Erro ao carregar página: {clientes_response.status_code}")

if __name__ == "__main__":
    test_alocacao()
