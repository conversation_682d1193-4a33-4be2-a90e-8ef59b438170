<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
    Cadastrar Funcionário - Controle de Ponto
</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/static/css/relatorios.css" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/style-cadastrar.css">
    
<!-- 🔧 CORREÇÃO CRÍTICA: CSS Fix para Modal Embaçado - Context7 MCP -->
<link rel="stylesheet" href="/static/css/modal-biometria-fix.css">
<style>
    .form-container {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .form-header {
        border-bottom: 2px solid #4fbdba;
        padding-bottom: 15px;
        margin-bottom: 25px;
    }
    
    .form-header h2 {
        color: #495057;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
    }
    
    .form-section {
        margin-bottom: 30px;
    }
    
    .section-title {
        color: #495057;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .form-grid-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .form-grid-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: #f8f9fa;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #4fbdba;
        background: white;
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }
    
    .form-group.required label::after {
        content: " *";
        color: #dc3545;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    .error-list {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }
    
    .error-list h4 {
        margin: 0 0 10px 0;
        font-size: 16px;
    }
    
    .error-list ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
        margin-top: 30px;
    }
    
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-primary {
        background: #4fbdba;
        color: white;
    }
    
    .btn-primary:hover {
        background: #3da8a6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #545b62;
        color: white;
    }
    
    .btn-success {
        background: #28a745;
        color: white;
    }
    
    .btn-success:hover {
        background: #218838;
    }
    
    .biometria-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    
    .biometria-button {
        background: #4fbdba;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        margin-top: 10px;
    }
    
    /* 🦺 Estilos EPIs - Inspirado em MCP UI Inspirations @21st-dev/magic */
    .epis-management {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #e9ecef;
    }
    
    .epis-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .epis-header p {
        margin: 0;
        color: #6c757d;
        font-size: 14px;
    }
    
    .btn-add-epi {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 10px 18px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }
    
    .btn-add-epi:hover {
        background: linear-gradient(135deg, #218838, #1ea180);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
    }
    
    .epi-item {
        margin-bottom: 20px;
        animation: slideInUp 0.3s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .epi-card {
        background: white;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }
    
    .epi-card:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border-color: #4fbdba;
    }
    
    .epi-header {
        display: flex;
        align-items: center;
        padding: 16px;
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-bottom: 1px solid #e9ecef;
    }
    
    .epi-icon {
        font-size: 24px;
        margin-right: 12px;
        padding: 8px;
        background: linear-gradient(135deg, #4fbdba, #40a8a5);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        filter: drop-shadow(0 2px 4px rgba(79, 189, 186, 0.3));
    }
    
    .epi-info {
        flex: 1;
    }
    
    .epi-name {
        font-size: 16px;
        font-weight: 600;
        color: #343a40;
        margin: 0 0 4px 0;
    }
    
    .epi-ca {
        background: #e9ecef;
        color: #6c757d;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .btn-remove-epi {
        background: #dc3545;
        color: white;
        border: none;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-remove-epi:hover {
        background: #c82333;
        transform: scale(1.1);
    }
    
    .epi-fields {
        padding: 20px;
    }
    
    .field-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 16px;
    }
    
    .epi-fields .form-group input,
    .epi-fields .form-group textarea {
        background: #fafbfc;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .epi-fields .form-group input:focus,
    .epi-fields .form-group textarea:focus {
        background: white;
        border-color: #4fbdba;
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }
    
    .epis-empty {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
    }
    
    .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.6;
    }
    
    .epis-empty h4 {
        font-size: 18px;
        margin: 0 0 8px 0;
        color: #495057;
    }
    
    .epis-empty p {
        margin: 0;
        font-size: 14px;
    }
    
    /* Responsividade para EPIs */
    @media (max-width: 768px) {
        .epis-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .field-row {
            grid-template-columns: 1fr;
        }
        
        .epi-header {
            flex-wrap: wrap;
        }
        }
    
    .biometria-button:hover {
        background: #3da8a6;
    }
    
    .biometria-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
        background-color: #dc3545;
    }
    
    .biometria-indicator.captured {
        background-color: #28a745;
    }

    /* Indicadores visuais para campos auto-completados */
    .auto-filled {
        background-color: #e7f3ff !important;
        border-color: #0066cc !important;
        transition: all 0.3s ease;
    }
    
    @media (max-width: 768px) {
        .form-grid,
        .form-grid-2,
        .form-grid-3 {
            grid-template-columns: 1fr;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .form-container {
            padding: 20px;
        }
    }
</style>

    <style>
        /* 🎨 MODERN SIDEBAR DESIGN - Inspirado no MCP @21st-dev/magic */
        
        :root {
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 70px;
            --primary-color: #4fbdba;
            --sidebar-bg: #ffffff;
            --sidebar-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --sidebar-border: #e5e7eb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --hover-bg: #f3f4f6;
            --active-bg: #e0f2f1;
            --active-text: #00695c;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        [data-theme="dark"] {
            --sidebar-bg: #1e293b;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --hover-bg: #334155;
            --active-bg: #0f172a;
            --sidebar-border: #334155;
        }

        /* Reset básico */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
            color: var(--text-primary);
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* 🎯 SIDEBAR MODERNA */
        .modern-sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            border-right: 1px solid var(--sidebar-border);
            box-shadow: var(--sidebar-shadow);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
        }

        .modern-sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        /* Header da Sidebar */
        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--sidebar-border);
            display: flex;
            align-items: center;
            gap: 12px;
            min-height: 80px;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), #26a69a);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            transition: var(--transition);
        }

        .collapsed .sidebar-title {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* Navigation */
        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 32px;
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0 20px 12px;
            transition: var(--transition);
        }

        .collapsed .nav-section-title {
            opacity: 0;
            height: 0;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .nav-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 4px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 20px;
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 0 24px 24px 0;
            margin-right: 20px;
            position: relative;
            transition: var(--transition);
            font-weight: 500;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .nav-link:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--active-bg);
            color: var(--active-text);
            box-shadow: 0 2px 8px rgba(79, 189, 186, 0.2);
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-color);
            border-radius: 0 4px 4px 0;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 18px;
        }

        .nav-text {
            font-size: 14px;
            transition: var(--transition);
        }

        .collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* Sidebar Footer */
        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid var(--sidebar-border);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 12px;
            background: var(--hover-bg);
            transition: var(--transition);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #26a69a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }

        .user-info {
            transition: var(--transition);
        }

        .collapsed .user-info {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .user-role {
            font-size: 12px;
            color: var(--text-secondary);
            margin: 0;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: var(--transition);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        .content-header {
            background: white;
            border-bottom: 1px solid var(--sidebar-border);
            padding: 16px 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .sidebar-toggle:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
        }

        .content-body {
            flex: 1;
            padding: 32px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .modern-sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width);
            }
            
            .modern-sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--transition);
            }
            
            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .collapsed ~ .main-content {
                margin-left: 0;
            }
            
            .content-header {
                padding: 16px 20px;
            }
            
            .content-body {
                padding: 20px;
            }
        }

        /* Estilos existentes mantidos para compatibilidade */
        .breadcrumbs {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .breadcrumbs a {
            color: #4fbdba;
            text-decoration: none;
        }
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        .breadcrumbs span {
            color: #6c757d;
            margin: 0 5px;
        }
        
        /* Flash messages */
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-weight: 500;
            border: 1px solid transparent;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .flash-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        .flash-info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .nav-link:hover .nav-icon {
            animation: slideIn 0.3s ease;
        }

        /* Badge para notificações */
        .nav-badge {
            background: #ef4444;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            position: absolute;
            top: 8px;
            right: 16px;
        }

        /* Scroll customizado */
        .modern-sidebar::-webkit-scrollbar {
            width: 4px;
        }
        
        .modern-sidebar::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .modern-sidebar::-webkit-scrollbar-thumb {
            background: var(--sidebar-border);
            border-radius: 4px;
        }
        
        .modern-sidebar::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* Compatibility with existing styles */
        .table-responsive, .data-table, .action-buttons, .btn-sm, .btn-view, .btn-edit, .btn-delete,
        .pagination, .badge, .filters, .filters-row, .filter-group {
            /* Mantém estilos existentes para tabelas e outros componentes */
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 🎨 SIDEBAR MODERNA -->
        <aside class="modern-sidebar" id="sidebar">
            <!-- Header da Sidebar -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-fingerprint"></i>
                </div>
                <div class="sidebar-title">
                    <div>RLPONTO</div>
                    <div style="font-size: 10px; color: var(--text-secondary); font-weight: 400;">vBeta</div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="sidebar-nav">
                <!-- Menu Principal -->
                <div class="nav-section">
                    <div class="nav-section-title">Principal</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/funcionarios'">
                                <div class="nav-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span class="nav-text">Funcionários</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/funcionarios/cadastrar'">
                                <div class="nav-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <span class="nav-text">Novo Funcionário</span>
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Registro de Ponto -->
                <div class="nav-section">
                    <div class="nav-section-title">Ponto</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/registro-ponto/biometrico'">
                                <div class="nav-icon">
                                    <i class="fas fa-fingerprint"></i>
                                </div>
                                <span class="nav-text">Ponto Biométrico</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/registro-ponto/manual'">
                                <div class="nav-icon">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <span class="nav-text">Ponto Manual</span>
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Controle de Período - APENAS PARA ADMINISTRADORES -->
                
                <div class="nav-section">
                    <div class="nav-section-title">Período de Apuração</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/controle-periodo/'">
                                <div class="nav-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <span class="nav-text">Dashboard</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/controle-periodo/decisoes'">
                                <div class="nav-icon">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <span class="nav-text">Classificar Horas</span>
                                <span class="nav-badge" id="decisoes-badge" style="display: none;">0</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/controle-periodo/fechamento'">
                                <div class="nav-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <span class="nav-text">Fechamento</span>
                            </button>
                        </li>
                    </ul>
                </div>
                

                <!-- Relatórios -->
                <div class="nav-section">
                    <div class="nav-section-title">Relatórios</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/relatorios/pontos'">
                                <div class="nav-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <span class="nav-text">Relatórios de Ponto</span>
                            </button>
                        </li>
                                                 <li class="nav-item">
                             <button class="nav-link" onclick="window.location.href='/relatorios/estatisticas'">
                                 <div class="nav-icon">
                                     <i class="fas fa-chart-bar"></i>
                                 </div>
                                 <span class="nav-text">Estatísticas</span>
                             </button>
                         </li>
                    </ul>
                </div>

                <!-- Configurações (apenas admin) -->
                
                <div class="nav-section">
                    <div class="nav-section-title">Administração</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/empresa-principal'">
                                <div class="nav-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <span class="nav-text">Empresa Principal</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/ponto-admin'">
                                <div class="nav-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <span class="nav-text">Ponto</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/configuracoes'">
                                <div class="nav-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <span class="nav-text">Configurações</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/configurar_usuarios'">
                                <div class="nav-icon">
                                    <i class="fas fa-users-cog"></i>
                                </div>
                                <span class="nav-text">Gerenciar Usuários</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/funcionarios-desligados'">
                                <div class="nav-icon">
                                    <i class="fas fa-user-times"></i>
                                </div>
                                <span class="nav-text">Funcionários Desligados</span>
                            </button>
                        </li>
                    </ul>
                </div>
                
            </nav>

            <!-- Footer da Sidebar -->
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">
                        A
                    </div>
                    <div class="user-info">
                        <p class="user-name">admin</p>
                        <p class="user-role">Admin</p>
                    </div>
                </div>
                <button class="nav-link" onclick="window.location.href='/logout'" style="margin-top: 12px; color: #ef4444;">
                    <div class="nav-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <span class="nav-text">Sair</span>
                </button>
            </div>
        </aside>

        <!-- Overlay para mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Header do Conteúdo -->
            <header class="content-header">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div style="display: flex; align-items: center; gap: 16px;">
                    <div style="font-size: 18px; font-weight: 600;">
                        Sistema de Controle de Ponto
                    </div>
                </div>
            </header>

            <!-- Corpo do Conteúdo -->
            <div class="content-body">
                <!-- Breadcrumbs -->
                
                <nav class="breadcrumbs">
                    
                        
                            <a href="/">Início</a>
                        
                        
                            <span>/</span>
                        
                    
                        
                            Cadastrar Funcionário
                        
                        
                    
                </nav>
                

                <!-- Mensagens Flash -->
                
                    
                

                <!-- Conteúdo principal -->
                
<div class="form-container">
    <div class="form-header">
        <h2>
            
                👤 Cadastrar Novo Funcionário
            
        </h2>
    </div>
    
    
    <div class="error-list">
        <h4>❌ Corrija os seguintes erros:</h4>
        <ul>
            
            <li>Cpf: CPF inválido</li>
            
        </ul>
    </div>
    
    
    <form method="POST" enctype="multipart/form-data">
        <!-- Dados Pessoais -->
        <div class="form-section">
            <h3 class="section-title">
                👤 Dados Pessoais
            </h3>
            
            <div class="form-grid">
                <div class="form-group required">
                    <label for="nome_completo">Nome Completo</label>
                    <input type="text" id="nome_completo" name="nome_completo" value="TESTE MINIMO 195516" 
                           placeholder="Ex: João da Silva Santos" required oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="cpf">CPF</label>
                    <input type="text" id="cpf" name="cpf" value="000.000.000-00" required maxlength="14" 
                           placeholder="000.000.000-00" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="rg">RG</label>
                    <input type="text" id="rg" name="rg" value="TM-195516" required maxlength="20"
                           placeholder="Ex: 12.345.678-9 ou MG-12.345.678" oninput="toUpperCase(this);">
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="data_nascimento">Data de Nascimento</label>
                    <input type="date" id="data_nascimento" name="data_nascimento" value="1990-01-01" required>
                </div>
                
                <div class="form-group required">
                    <label for="sexo">Sexo</label>
                    <select id="sexo" name="sexo" required onchange="atualizarNacionalidade()">
                        <option value="">Selecione...</option>
                        <option value="M" selected>Masculino</option>
                        <option value="F" >Feminino</option>
                        <option value="Outro" >Outro</option>
                    </select>
                </div>
                
                <div class="form-group required">
                    <label for="estado_civil">Estado Civil</label>
                    <select id="estado_civil" name="estado_civil" required>
                        <option value="">Selecione...</option>
                        <option value="Solteiro" selected>Solteiro(a)</option>
                        <option value="Casado" >Casado(a)</option>
                        <option value="Divorciado" >Divorciado(a)</option>
                        <option value="Viuvo" >Viúvo(a)</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group required">
                <label for="nacionalidade">Nacionalidade</label>
                <input type="text" id="nacionalidade" name="nacionalidade" value="Brasileira" 
                       placeholder="Ex: Brasileira, Americana, Italiana..." required oninput="toUpperCase(this);">
            </div>
        </div>
        
        <!-- Documentos Trabalhistas -->
        <div class="form-section">
            <h3 class="section-title">
                📋 Documentos Trabalhistas
            </h3>
            
            <div class="form-grid-3">
                <div class="form-group">
                    <label for="ctps_numero">CTPS Número</label>
                    <input type="text" id="ctps_numero" name="ctps_numero" value=""
                           placeholder="Ex: 1234567 (Opcional)" maxlength="7" oninput="toUpperCase(this);">
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle"></i> Campo opcional - pode ser preenchido posteriormente
                    </small>
                </div>

                <div class="form-group">
                    <label for="ctps_serie_uf">CTPS Série/UF</label>
                    <input type="text" id="ctps_serie_uf" name="ctps_serie_uf" value=""
                           placeholder="Ex: 001/MG ou 123-MG (Opcional)" maxlength="10" oninput="toUpperCase(this);">
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle"></i> Campo opcional - pode ser preenchido posteriormente
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="pis_pasep">
                        PIS/PASEP
                        <small class="text-muted">(Opcional)</small>
                    </label>
                    <input type="text" id="pis_pasep" name="pis_pasep" value="000.00000.00-0"
                           placeholder="000.00000.00-0" maxlength="14" oninput="toUpperCase(this);">
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle"></i> Campo opcional - padrão 000.00000.00-0
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Endereço -->
        <div class="form-section">
            <h3 class="section-title">
                🏠 Endereço
            </h3>
            
            <div class="form-grid-2">
                <div class="form-group required">
                    <label for="endereco_cep">CEP</label>
                    <input type="text" id="endereco_cep" name="endereco_cep" value="00000-000" 
                           required maxlength="9" placeholder="00000-000" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="endereco_estado">Estado</label>
                    <select id="endereco_estado" name="endereco_estado" required>
                        <option value="">Selecione...</option>
                        
                        <option value="AC" >AC</option>
                        
                        <option value="AL" >AL</option>
                        
                        <option value="AP" >AP</option>
                        
                        <option value="AM" >AM</option>
                        
                        <option value="BA" >BA</option>
                        
                        <option value="CE" >CE</option>
                        
                        <option value="DF" >DF</option>
                        
                        <option value="ES" >ES</option>
                        
                        <option value="GO" >GO</option>
                        
                        <option value="MA" >MA</option>
                        
                        <option value="MT" >MT</option>
                        
                        <option value="MS" >MS</option>
                        
                        <option value="MG" >MG</option>
                        
                        <option value="PA" >PA</option>
                        
                        <option value="PB" >PB</option>
                        
                        <option value="PR" >PR</option>
                        
                        <option value="PE" >PE</option>
                        
                        <option value="PI" >PI</option>
                        
                        <option value="RJ" >RJ</option>
                        
                        <option value="RN" >RN</option>
                        
                        <option value="RS" >RS</option>
                        
                        <option value="RO" >RO</option>
                        
                        <option value="RR" >RR</option>
                        
                        <option value="SC" >SC</option>
                        
                        <option value="SP" selected>SP</option>
                        
                        <option value="SE" >SE</option>
                        
                        <option value="TO" >TO</option>
                        
                    </select>
                </div>
            </div>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="endereco_rua">Rua/Logradouro</label>
                    <input type="text" id="endereco_rua" name="endereco_rua" value=""
                           placeholder="Ex: Rua das Flores, 123, Apt 45" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="endereco_bairro">Bairro</label>
                    <input type="text" id="endereco_bairro" name="endereco_bairro" value=""
                           placeholder="Ex: Centro, Vila Nova, Jardim América" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="endereco_cidade">Cidade</label>
                    <input type="text" id="endereco_cidade" name="endereco_cidade" value=""
                           placeholder="Ex: São Paulo, Belo Horizonte, Rio de Janeiro" oninput="toUpperCase(this);">
                </div>
            </div>
        </div>
        
        <!-- Contato -->
        <div class="form-section">
            <h3 class="section-title">
                📞 Contato
            </h3>
            
            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="telefone1">Telefone Principal</label>
                    <input type="tel" id="telefone1" name="telefone1" value="(11) 99999-9999" required 
                           placeholder="(11) 99999-9999" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="telefone2">Telefone Secundário</label>
                    <input type="tel" id="telefone2" name="telefone2" value="" 
                           placeholder="(11) 3333-4444 (Opcional)" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="email">E-mail</label>
                    <input type="email" id="email" name="email" value="" 
                           placeholder="<EMAIL>" oninput="toLowerCase(this);">
                </div>
            </div>
        </div>
        
        <!-- Dados Profissionais -->
        <div class="form-section">
            <h3 class="section-title">
                💼 Dados Profissionais
            </h3>

            <div class="form-grid">
                <div class="form-group required">
                    <label for="empresa_id">Empresa</label>
                    <select id="empresa_id" name="empresa_id" required>
                        <option value="">Selecione a empresa...</option>
                        
                            
                                <option value="4"
                                        selected
                                        data-principal="true">
                                    ⭐ Msv Engenharia e Construcao - Renovar Construcao Civil Ltda
                                </option>
                            
                                <option value="11"
                                        
                                        >
                                    AiNexus Tecnologia - AiNexus Tecnologia
                                </option>
                            
                                <option value="14"
                                        
                                        >
                                     - Ocrim S.A. Produtos Alimentícios
                                </option>
                            
                        
                    </select>
                    <small class="form-text text-muted">
                        <i class="fas fa-star"></i> A empresa marcada com ⭐ é a empresa principal
                    </small>
                </div>

                <div class="form-group required">
                    <label for="cargo">Cargo</label>
                    <input type="text" id="cargo" name="cargo" value="TESTE MINIMO" required
                           placeholder="Ex: Analista, Operador, Técnico, Auxiliar"
                           list="cargos_sugeridos" oninput="toUpperCase(this);">
                    <datalist id="cargos_sugeridos">
                        <option value="Analista Administrativo">
                        <option value="Auxiliar Administrativo">
                        <option value="Técnico em Segurança">
                        <option value="Operador de Máquinas">
                        <option value="Servente de Obras">
                        <option value="Pedreiro">
                        <option value="Eletricista">
                        <option value="Soldador">
                        <option value="Motorista">
                        <option value="Vigilante">
                    </datalist>
                </div>
                
                <div class="form-group required">
                    <label for="setor_obra">Setor/Obra</label>
                    <input type="text" id="setor_obra" name="setor_obra" value="TESTE" required
                           placeholder="Ex: Administrativo, Produção, Obra Centro"
                           list="setores_sugeridos" oninput="toUpperCase(this);">
                    <datalist id="setores_sugeridos">
                        <option value="Administrativo">
                        <option value="Financeiro">
                        <option value="Recursos Humanos">
                        <option value="Produção">
                        <option value="Manutenção">
                        <option value="Segurança">
                        <option value="Obra Centro">
                        <option value="Obra Norte">
                        <option value="Obra Sul">
                    </datalist>
                </div>
                
                <div class="form-group required">
                    <label for="matricula_empresa">Matrícula</label>
                    <input type="text" id="matricula_empresa" name="matricula_empresa"
                           value="TM195516" required readonly
                           placeholder="Gerado automaticamente"
                           style="background-color: #f8f9fa; cursor: not-allowed;"
                           title="Matrícula gerada automaticamente - não editável">
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle"></i> Matrícula gerada automaticamente de forma sequencial e única
                    </small>
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="data_admissao">Data Admissão</label>
                    <input type="date" id="data_admissao" name="data_admissao" value="2025-07-20" required>
                </div>
                
                <div class="form-group required">
                    <label for="tipo_contrato">Tipo Contrato</label>
                    <select id="tipo_contrato" name="tipo_contrato" required>
                        <option value="">Selecione...</option>
                        <option value="CLT" selected>CLT (Consolidação das Leis do Trabalho)</option>
                        <option value="PJ" >PJ (Pessoa Jurídica)</option>
                        <option value="Estagio" >Estágio</option>
                        <option value="Temporario" >Temporário</option>
                    </select>
                </div>
                
                <div class="form-group required">
                    <label for="status_cadastro">Status</label>
                    <select id="status_cadastro" name="status_cadastro" required>
                        <option value="Ativo" selected>Ativo</option>
                        <option value="Inativo" >Inativo</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- ✅ SIMPLIFICADO: Configurações Básicas -->
        <div class="form-section">
            <h3 class="section-title">
                ⚙️ Configurações
            </h3>

            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="horas_semanais_obrigatorias">
                        <i class="fas fa-calendar-week"></i> Horas Semanais Obrigatórias
                        <span class="text-danger">*</span>
                    </label>
                    <input type="number"
                           id="horas_semanais_obrigatorias"
                           name="horas_semanais_obrigatorias"
                           value="44.0"
                           min="10"
                           max="60"
                           step="0.25"
                           required
                           class="form-control"
                           lang="en-US"
                           data-decimal-separator="."
                           
                           placeholder="44.00"
                           pattern="[0-9]+(\.[0-9]{1,2})?"
                           title="Use formato decimal com ponto (ex: 44.00)">
                    <small class="form-text text-muted">
                        <strong>Exemplos:</strong> 44.00 (CLT padrão), 30.00 (meio período), 40.00 (empresa específica)<br>
                        <em>Definido conforme contrato de trabalho do funcionário</em>
                    </small>
                </div>
                
                <div class="form-group required">
                    <label for="nivel_acesso">
                        <i class="fas fa-user-shield"></i> Nível de Acesso
                        <span class="text-danger">*</span>
                    </label>
                    <select id="nivel_acesso" name="nivel_acesso" required class="form-control">
                        <option value="">Selecione...</option>
                        <option value="Funcionario" selected>Funcionário</option>
                        <option value="Supervisao" >Supervisão</option>
                        <option value="Gerencia" >Gerência</option>
                    </select>
                </div>

                <div class="form-group">
                    <label style="margin-bottom: 10px;">
                        <i class="fas fa-cog"></i> Opções Adicionais
                    </label>
                    <div class="checkbox-group" style="margin-bottom: 8px;">
                        <input type="checkbox" id="banco_horas" name="banco_horas" checked>
                        <label for="banco_horas">Banco de Horas</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="hora_extra" name="hora_extra" checked>
                        <label for="hora_extra">Hora Extra</label>
                    </div>
                </div>


            </div>
        </div>
        
        <!-- Remuneração e Pagamento -->
        <div class="form-section">
            <h3 class="section-title">
                💰 Remuneração e Pagamento
            </h3>

            <div class="alert alert-info" style="margin-bottom: 20px;">
                <i class="fas fa-info-circle"></i>
                <strong>Informação:</strong> Todos os campos desta seção são <strong>opcionais e informativos</strong>.
                Podem ser preenchidos posteriormente ou deixados em branco conforme a política da empresa.
            </div>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="salario_base">
                        <i class="fas fa-dollar-sign"></i> Salário Base (R$)
                        <small class="text-muted">(Opcional)</small>
                    </label>
                    <input type="number" id="salario_base" name="salario_base"
                           value=""
                           step="0.01" min="0" max="999999.99"
                           placeholder="1400.00" onchange="calcularValorHora()">
                    <small class="form-text text-muted">Campo informativo - não obrigatório</small>
                </div>
                
                <div class="form-group">
                    <label for="tipo_pagamento">Tipo de Pagamento</label>
                    <select id="tipo_pagamento" name="tipo_pagamento">
                        <option value="Mensal" selected>Mensal</option>
                        <option value="Quinzenal" >Quinzenal</option>
                        <option value="Semanal" >Semanal</option>
                        <option value="Diario" >Diário</option>
                        <option value="Por_Hora" >Por Hora</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="valor_hora">Valor da Hora (R$)</label>
                    <input type="number" id="valor_hora" name="valor_hora" 
                           value="" 
                           step="0.01" min="0" max="9999.99"
                           placeholder="6.36" readonly style="background: #f8f9fa;">
                    <small style="color: #6c757d; font-size: 12px;">Calculado automaticamente: Salário ÷ 220h/mês</small>
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group">
                    <label for="percentual_hora_extra">% Hora Extra</label>
                    <input type="number" id="percentual_hora_extra" name="percentual_hora_extra" 
                           value="50.0" 
                           step="0.01" min="0" max="200"
                           placeholder="50.00" onchange="calcularValorHoraExtra()">
                </div>
                
                <div class="form-group">
                    <label for="valor_hora_extra">Valor Hora Extra (R$)</label>
                    <input type="number" id="valor_hora_extra" name="valor_hora_extra" 
                           value="" 
                           step="0.01" min="0" max="9999.99"
                           placeholder="9.54" readonly style="background: #f8f9fa;">
                    <small style="color: #6c757d; font-size: 12px;">Calculado: Valor hora + percentual</small>
                </div>
                
                <div class="form-group">
                    <label for="vale_transporte">Vale Transporte (R$)</label>
                    <input type="number" id="vale_transporte" name="vale_transporte" 
                           value="" 
                           step="0.01" min="0" max="999.99"
                           placeholder="150.00">
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group">
                    <label for="vale_alimentacao">Vale Alimentação (R$)</label>
                    <input type="number" id="vale_alimentacao" name="vale_alimentacao" 
                           value="" 
                           step="0.01" min="0" max="999.99"
                           placeholder="400.00">
                </div>
                
                <div class="form-group">
                    <label for="outros_beneficios">Outros Benefícios (R$)</label>
                    <input type="number" id="outros_beneficios" name="outros_beneficios" 
                           value="" 
                           step="0.01" min="0" max="9999.99"
                           placeholder="0.00">
                </div>
                
                <div class="form-group" style="display: flex; flex-direction: column; gap: 10px;">
                    <label>Descontos</label>
                    <div class="checkbox-group">
                        <input type="checkbox" id="desconto_inss" name="desconto_inss" 
                               >
                        <label for="desconto_inss">INSS</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="desconto_irrf" name="desconto_irrf" 
                               >
                        <label for="desconto_irrf">IRRF</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="observacoes_pagamento">Observações sobre Pagamento</label>
                <textarea id="observacoes_pagamento" name="observacoes_pagamento" 
                          rows="3" style="resize: vertical;"
                          placeholder="Informações adicionais sobre pagamento, benefícios específicos, comissões, etc."></textarea>
            </div>
        </div>
        
        <!-- Biometria e Foto -->
        <div class="form-section">
            <h3 class="section-title">
                🔐 Biometria e Foto
            </h3>
            
            <!-- Foto 3x4 -->
            <div class="form-group" style="margin-bottom: 30px;">
                <label for="foto_3x4">Foto 3x4 (Opcional)</label>
                <div style="display: flex; align-items: center; gap: 20px; margin-top: 10px;">
                    <div>
                        <input type="file" id="foto_3x4" name="foto_3x4" accept="image/*" style="margin-bottom: 10px;">
                        <div style="font-size: 12px; color: #6c757d;">
                            Formatos aceitos: JPG, PNG, GIF. Tamanho máximo: 5MB
                        </div>
                    </div>
                    <div>
                        <img id="preview_foto" src="/static/images/funcionario_sem_foto.svg" 
                             alt="Preview da foto" 
                             style="width: 120px; height: 160px; border: 1px solid #dee2e6; border-radius: 6px; object-fit: cover;">
                    </div>
                </div>
            </div>
            
            <!-- Novo botão de biometria -->
            <div class="biometria-section">
                <div id="biometriaStatusSection">
                    
                    <!-- ❌ SEM BIOMETRIA - Captura normal -->
                    <div class="biometria-status-vazia" style="background: #fff3cd; border: 2px solid #ffc107; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 24px;">⚠️</span>
                            <div>
                                <h4 style="margin: 0; color: #856404;">Biometria Não Configurada</h4>
                                <p style="margin: 0; color: #856404; font-size: 14px;">
                                    Este funcionário ainda não possui biometria registrada para controle de acesso.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="abrirModalBiometria()">
                        🔐 Capturar Biometria
                    </button>
                    
                </div>
                
                <!-- Campos hidden preservados -->
                <input type="hidden" id="digital_dedo1" name="digital_dedo1" value="">
                <input type="hidden" id="digital_dedo2" name="digital_dedo2" value="">
            </div>
        </div>
        
        <!-- 🔧 CORREÇÃO: Modal de Biometria com estado inicial correto -->
        <div id="modalBiometria" class="modal-biometria" style="display:none;" aria-hidden="true">
            <div class="modal-backdrop"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h2>🔐 Captura Biométrica</h2>
                    <button type="button" class="modal-close" onclick="fecharModalBiometria()">&times;</button>
                </div>
                
                <div class="modal-content">
                    <!-- Status e Progresso -->
                    <div class="biometria-status">
                        <div class="status-bar">
                            <div class="status-step active" id="step-1">
                                <div class="step-number">1</div>
                                <div class="step-label">Conexão</div>
                            </div>
                            <div class="status-step" id="step-2">
                                <div class="step-number">2</div>
                                <div class="step-label">Dedo 1</div>
                            </div>
                            <div class="status-step" id="step-3">
                                <div class="step-number">3</div>
                                <div class="step-label">Dedo 2</div>
                            </div>
                            <div class="status-step" id="step-4">
                                <div class="step-number">4</div>
                                <div class="step-label">Concluído</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Área de Visualização Biométrica -->
                    <div class="biometria-display">
                        <div class="biometria-scanner">
                            <div class="scanner-frame">
                                <div class="scanner-area" id="scannerArea">
                                    <div class="fingerprint-icon">
                                        <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                                            <path d="M60 20C45.9 20 34.5 31.4 34.5 45.5C34.5 52.1 36.8 58.2 40.7 62.9L60 95L79.3 62.9C83.2 58.2 85.5 52.1 85.5 45.5C85.5 31.4 74.1 20 60 20Z" stroke="#007bff" stroke-width="2" fill="rgba(0,123,255,0.1)"/>
                                            <circle cx="60" cy="45" r="15" stroke="#007bff" stroke-width="2" fill="none"/>
                                            <path d="M45 45C45 36.7 51.7 30 60 30" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                            <path d="M50 45C50 39.5 54.5 35 60 35" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                            <path d="M55 45C55 42.2 57.2 40 60 40" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                        </svg>
                                    </div>
                                    <div class="scanner-pulse" id="scannerPulse"></div>
                                </div>
                                <div class="scanner-label">Coloque o dedo no leitor ZK4500</div>
                            </div>
                        </div>
                        
                        <!-- Área de Instruções -->
                        <div class="biometria-instructions">
                            <div class="instruction-card" id="instructionCard">
                                <h4>Preparação</h4>
                                <ul>
                                    <li>Conecte o leitor ZK4500 via USB</li>
                                    <li>Certifique-se que o dedo está limpo e seco</li>
                                    <li>Posicione o dedo centralmente no leitor</li>
                                    <li>Mantenha pressão constante até o sinal</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resultado das Capturas -->
                    <div class="capturas-resultado" id="capturasResultado" style="display:none;">
                        <div class="resultado-grid">
                            <div class="resultado-card" id="resultadoDedo1">
                                <div class="resultado-header">
                                    <h5>Dedo 1</h5>
                                    <span class="status-badge pending">Aguardando</span>
                                </div>
                                <div class="qualidade-meter">
                                    <div class="qualidade-bar">
                                        <div class="qualidade-fill" data-qualidade="0"></div>
                                    </div>
                                    <span class="qualidade-valor">0%</span>
                                </div>
                            </div>
                            
                            <div class="resultado-card" id="resultadoDedo2">
                                <div class="resultado-header">
                                    <h5>Dedo 2</h5>
                                    <span class="status-badge pending">Aguardando</span>
                                </div>
                                <div class="qualidade-meter">
                                    <div class="qualidade-bar">
                                        <div class="qualidade-fill" data-qualidade="0"></div>
                                    </div>
                                    <span class="qualidade-valor">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Geral -->
                    <div class="status-message">
                        <div id="statusBiometria" class="status-text">
                            Clique em "Iniciar Captura" para começar
                        </div>
                    </div>
                </div>
                
                <!-- Ações do Modal -->
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="fecharModalBiometria()">
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="btnIniciarCaptura" onclick="iniciarCapturaBiometria()">
                        Iniciar Captura
                    </button>
                    <button type="button" class="btn btn-success" id="btnSalvarBiometria" onclick="salvarBiometriaModal()" style="display:none;">
                        Salvar Biometria
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 🦺 Seção de EPIs - Baseada em MCP UI Inspirations @21st-dev/magic -->
        <div class="form-section">
            <div class="section-title">
                🦺 Equipamentos de Proteção Individual (EPIs)
            </div>
            
            <!-- Container dos EPIs dinâmicos -->
            <div id="epis-container" class="epis-management">
                <div class="epis-header">
                    <p class="text-muted">Gerencie os EPIs deste funcionário diretamente durante o cadastro</p>
                    <button type="button" 
                            class="btn-add-epi" 
                            onclick="adicionarNovoEPI()"
                            title="Adicionar novo EPI">
                        ➕ Adicionar EPI
                    </button>
                </div>
                
                <!-- Lista de EPIs existentes (em modo edição) -->
                
                
                <!-- Mensagem quando não há EPIs -->
                <div id="epis-empty-state" class="epis-empty ">
                    <div class="empty-icon">📦</div>
                    <h4>Nenhum EPI adicionado</h4>
                    <p>Clique em "Adicionar EPI" para incluir equipamentos de proteção individual</p>
                </div>
            </div>
        </div>

        <!-- Ações -->
        <div class="form-actions">
            <a href="/funcionarios" class="btn btn-secondary">
                ← Cancelar
            </a>
            <button type="submit" class="btn btn-primary">
                
                    ✅ Cadastrar Funcionário
                
            </button>
        </div>
    </form>
</div>

                
                <!-- Rodapé com informações do sistema -->
                <footer class="system-footer">
                    <div class="footer-content">
                        <div class="footer-line">RLPONTO vBeta • Sistema de Controle de Ponto</div>
                        <div class="footer-line">© 2025 AiNexus Tecnologia. Todos os direitos reservados.</div>
                    </div>
                </footer>
            </div>
        </main>
    </div>
    
    <!-- Modal de confirmação -->
    <div id="confirmModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; max-width: 400px; width: 90%;">
            <h3 style="margin-top: 0; color: #dc3545;">Confirmar Exclusão</h3>
            <p id="confirmMessage">Tem certeza que deseja excluir este funcionário?</p>
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                <button onclick="closeConfirmModal()" style="padding: 8px 16px; border: 1px solid #ced4da; background: white; border-radius: 4px; cursor: pointer;">Cancelar</button>
                <button id="confirmDelete" style="padding: 8px 16px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">Excluir</button>
            </div>
        </div>
    </div>
    
    <!-- jQuery (necessário para DataTables) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    
<!-- Script de funcionalidades do formulário -->
<script src="/static/js/cadastro-funcionarios.js"></script>
<!-- Script de biometria ZKAgent -->
<script src="/static/js/biometria-zkagent.js"></script>

<!-- 🦺 JavaScript para EPIs - Inspirado em MCP UI Inspirations @21st-dev/magic -->
<script>
// Inicializar contador de EPIs baseado nos dados do servidor
let episCount = 0;

function adicionarNovoEPI() {
    const container = document.getElementById('epis-container');
    const emptyState = document.getElementById('epis-empty-state');
    
    // Ocultar mensagem vazia
    emptyState.style.display = 'none';
    
    // Criar novo item EPI
    const episItem = document.createElement('div');
    episItem.className = 'epi-item';
    episItem.setAttribute('data-epi-id', 'new-' + episCount);
    
    episItem.innerHTML = `
        <div class="epi-card">
            <div class="epi-header">
                <div class="epi-icon">🦺</div>
                <div class="epi-info">
                    <h5 class="epi-name">Novo EPI</h5>
                </div>
                <button type="button"
                        class="btn-remove-epi"
                        onclick="removerEPI(this)"
                        title="Remover EPI">
                    ❌
                </button>
            </div>

            <div class="epi-fields">
                <div class="field-row">
                    <div class="form-group">
                        <label>Nome do EPI *</label>
                        <input type="text"
                               name="epis[` + episCount + `][epi_nome]"
                               placeholder="Ex: Capacete de Segurança">
                    </div>
                    <div class="form-group">
                        <label>C.A. (Certificado)</label>
                        <input type="text"
                               name="epis[` + episCount + `][epi_ca]"
                               placeholder="Ex: 12345">
                    </div>
                </div>

                <div class="field-row">
                    <div class="form-group">
                        <label>Data de Entrega</label>
                        <input type="date"
                               name="epis[` + episCount + `][epi_data_entrega]">
                    </div>
                    <div class="form-group">
                        <label>Data de Validade</label>
                        <input type="date"
                               name="epis[` + episCount + `][epi_data_validade]">
                    </div>
                </div>

                <div class="form-group">
                    <label>Observações</label>
                    <textarea name="epis[` + episCount + `][epi_observacoes]"
                              rows="2"
                              placeholder="Observações sobre o EPI..."></textarea>
                </div>
            </div>
        </div>
    `;
    
    // Inserir antes da mensagem vazia
    container.insertBefore(episItem, emptyState);
    
    // Focar no campo nome do EPI
    const nomeInput = episItem.querySelector('input[name*="epi_nome"]');
    if (nomeInput) {
        nomeInput.focus();
        
        // Atualizar o nome na header quando digitar
        nomeInput.addEventListener('input', function() {
            const nomeEpi = episItem.querySelector('.epi-name');
            nomeEpi.textContent = this.value || 'Novo EPI';
        });
    }
    
    // Atualizar C.A. na header quando digitar
    const caInput = episItem.querySelector('input[name*="epi_ca"]');
    if (caInput) {
        caInput.addEventListener('input', function() {
            let caSpan = episItem.querySelector('.epi-ca');
            if (this.value) {
                if (!caSpan) {
                    caSpan = document.createElement('span');
                    caSpan.className = 'epi-ca';
                    episItem.querySelector('.epi-info').appendChild(caSpan);
                }
                caSpan.textContent = 'C.A: ' + this.value;
            } else if (caSpan) {
                caSpan.remove();
            }
        });
    }
    
    episCount++;
}

function removerEPI(botao) {
    const episItem = botao.closest('.epi-item');
    const container = document.getElementById('epis-container');
    const emptyState = document.getElementById('epis-empty-state');
    
    // Animação de saída
    episItem.style.animation = 'slideOutDown 0.3s ease-in';
    
    setTimeout(() => {
        episItem.remove();
        
        // Verificar se ainda há EPIs
        const remainingEpis = container.querySelectorAll('.epi-item');
        if (remainingEpis.length === 0) {
            emptyState.style.display = 'block';
        }
        
        // Reorganizar os índices dos campos
        reorganizarIndicesEPIs();
    }, 300);
}

function reorganizarIndicesEPIs() {
    const episItems = document.querySelectorAll('.epi-item');
    
    episItems.forEach((item, index) => {
        const inputs = item.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            if (input.name) {
                // Atualizar o índice no name do campo
                input.name = input.name.replace(/epis\[\d+\]/, `epis[${index}]`);
            }
        });
    });
    
    episCount = episItems.length;
}

// Animação CSS adicional para remoção
const additionalCSS = `
@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}
`;

// Adicionar CSS dinamicamente
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalCSS;
document.head.appendChild(styleSheet);

// ✅ SIMPLIFICADO: Remoção da verificação de jornadas
// A verificação de jornadas foi removida para simplificar o cadastro

// ✅ ATUALIZADO: Garantir formato decimal com ponto para horas semanais
document.addEventListener('DOMContentLoaded', function() {
    const horasInput = document.getElementById('horas_semanais_obrigatorias');

    if (horasInput) {
        // ✅ CORREÇÃO: Apenas converter vírgula para ponto, SEM forçar valor padrão
        setTimeout(function() {
            // Apenas converter vírgula para ponto se houver valor
            if (horasInput.value) {
                horasInput.value = horasInput.value.replace(',', '.');
            }
            // ✅ NOVO: Só definir padrão se realmente estiver vazio (novo cadastro)
            else if (!horasInput.value && !horasInput.getAttribute('data-editing')) {
                horasInput.value = '44.00';
            }
        }, 100);

        // Converter vírgula para ponto ao digitar
        horasInput.addEventListener('input', function() {
            let value = this.value.replace(',', '.');
            this.value = value;
        });

        // ✅ CORREÇÃO: Garantir formato correto ao sair do campo
        horasInput.addEventListener('blur', function() {
            let value = parseFloat(this.value.replace(',', '.'));
            if (!isNaN(value) && value >= 10 && value <= 60) {
                this.value = value.toFixed(2);
            } else if (isNaN(value) || value < 10 || value > 60) {
                // ✅ CORREÇÃO: Usar 44.00 como padrão (não 8.00)
                this.value = '44.00';
            }
        });

        // ✅ FORÇAR correção a cada 500ms nos primeiros 3 segundos
        let corrections = 0;
        const forceCorrection = setInterval(function() {
            if (horasInput.value.includes(',')) {
                horasInput.value = horasInput.value.replace(',', '.');
            }
            corrections++;
            if (corrections >= 6) { // 6 x 500ms = 3 segundos
                clearInterval(forceCorrection);
            }
        }, 500);
    }
});
</script>


    <script>
        // 🚀 SIDEBAR CONTROLS
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const mainContent = document.querySelector('.main-content');

        let isMobile = window.innerWidth <= 768;
        let isCollapsed = false;

        // Toggle sidebar
        function toggleSidebar() {
            if (isMobile) {
                sidebar.classList.toggle('mobile-open');
                sidebarOverlay.classList.toggle('show');
            } else {
                isCollapsed = !isCollapsed;
                sidebar.classList.toggle('collapsed', isCollapsed);
                
                // Salvar preferência
                localStorage.setItem('sidebarCollapsed', isCollapsed);
            }
        }

        // Event listeners
        sidebarToggle.addEventListener('click', toggleSidebar);
        sidebarOverlay.addEventListener('click', toggleSidebar);

        // Responsive handling
        window.addEventListener('resize', () => {
            const wasMobile = isMobile;
            isMobile = window.innerWidth <= 768;
            
            if (wasMobile !== isMobile) {
                // Reset sidebar state when switching between mobile/desktop
                sidebar.classList.remove('mobile-open', 'collapsed');
                sidebarOverlay.classList.remove('show');
                
                if (!isMobile) {
                    // Restore collapsed state on desktop
                    isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    sidebar.classList.toggle('collapsed', isCollapsed);
                }
            }
        });

        // Restore sidebar state on load
        document.addEventListener('DOMContentLoaded', () => {
            if (!isMobile) {
                isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                sidebar.classList.toggle('collapsed', isCollapsed);
            }
            
            // Highlight active nav item
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const onclick = link.getAttribute('onclick');
                if (onclick && onclick.includes(currentPath)) {
                    link.classList.add('active');
                }
            });
        });

        // Função para modal de confirmação (mantida para compatibilidade)
        function showConfirmModal(message, onConfirm) {
            document.getElementById('confirmMessage').textContent = message;
            document.getElementById('confirmModal').style.display = 'block';
            
            document.getElementById('confirmDelete').onclick = function() {
                closeConfirmModal();
                onConfirm();
            };
        }
        
        function closeConfirmModal() {
            document.getElementById('confirmModal').style.display = 'none';
        }
        
        // Fechar modal clicando fora
        document.getElementById('confirmModal').onclick = function(e) {
            if (e.target === this) {
                closeConfirmModal();
            }
        };
        
        // Função confirmarExclusao removida - agora cada página define sua própria versão
        // para permitir funcionalidades específicas (ex: desligamento profissional)
        
        // Auto-hide para mensagens flash
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>