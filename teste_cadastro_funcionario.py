#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste Automatizado - Cadastro de Funcionário
Sistema: RLPONTO-WEB
Data: 20/07/2025
"""

import requests
import json
from datetime import datetime

# Configurações
BASE_URL = "http://10.19.208.31:5000"
LOGIN_URL = f"{BASE_URL}/login"
CADASTRO_URL = f"{BASE_URL}/funcionarios/cadastrar"

# Credenciais
CREDENTIALS = {
    'usuario': 'admin',
    'senha': '@Ric6109'
}

def fazer_login():
    """Fazer login no sistema"""
    print("🔐 Fazendo login...")
    
    session = requests.Session()
    
    # Primeiro, obter a página de login para pegar o CSRF token se necessário
    response = session.get(LOGIN_URL)
    if response.status_code != 200:
        print(f"❌ Erro ao acessar página de login: {response.status_code}")
        return None
    
    # Fazer login
    login_data = {
        'usuario': CREDENTIALS['usuario'],
        'senha': CREDENTIALS['senha']
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code in [302, 200]:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print(f"❌ Erro no login: {response.status_code}")
        return None

def cadastrar_funcionario(session):
    """Cadastrar funcionário de teste"""
    print("👤 Cadastrando funcionário de teste...")
    
    timestamp = datetime.now().strftime("%H%M%S")
    
    # Dados do funcionário
    dados_funcionario = {
        'nome_completo': f'TESTE AUTOMATIZADO {timestamp}',
        'cpf': '000.000.000-00',
        'rg': f'TEST-{timestamp}',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'pis_pasep': '000.00000.00-0',
        'endereco_rua': 'Rua Teste',
        'endereco_bairro': 'Bairro Teste',
        'endereco_cidade': 'Cidade Teste',
        'endereco_cep': '00000-000',
        'endereco_estado': 'SP',
        'telefone1': '(11) 99999-9999',
        'telefone2': '',
        'email': f'teste{timestamp}@teste.com',
        'cargo': 'TESTE AUTOMATIZADO',
        'setor_obra': 'TESTE',
        'matricula_empresa': f'AUTO{timestamp}',
        'data_admissao': datetime.now().strftime('%Y-%m-%d'),
        'tipo_contrato': 'CLT',
        'nivel_acesso': 'Funcionario',
        'status_cadastro': 'Ativo',  # EXPLICITAMENTE ATIVO
        'empresa_id': '1',
        'turno': 'Diurno',
        'tolerancia_ponto': '10',
        'banco_horas': '',  # Não marcado
        'hora_extra': '',   # Não marcado
        'horas_semanais_obrigatorias': '44.00'
    }
    
    print(f"📝 Dados do funcionário:")
    print(f"   Nome: {dados_funcionario['nome_completo']}")
    print(f"   Status: {dados_funcionario['status_cadastro']}")
    print(f"   Empresa ID: {dados_funcionario['empresa_id']}")
    
    # Enviar dados
    response = session.post(CADASTRO_URL, data=dados_funcionario, allow_redirects=False)
    
    print(f"📤 Resposta do servidor: {response.status_code}")
    
    if response.status_code == 302:
        # Redirect - provavelmente sucesso
        location = response.headers.get('Location', '')
        print(f"✅ Redirect para: {location}")

        if '/funcionarios/' in location and '/detalhes' in location:
            # Extrair ID do funcionário da URL
            try:
                funcionario_id = location.split('/funcionarios/')[1].split('/')[0]
                print(f"✅ Funcionário criado com ID: {funcionario_id}")
                return funcionario_id
            except:
                print("⚠️ Não foi possível extrair ID do funcionário")
                return None
        else:
            print(f"⚠️ Redirect inesperado: {location}")
            return None
    else:
        print(f"❌ Erro no cadastro: {response.status_code}")

        # Procurar por mensagens de erro na página
        content = response.text
        if 'alert-danger' in content or 'error' in content.lower():
            print("🔍 Procurando mensagens de erro...")
            # Tentar extrair mensagens de erro
            import re
            error_patterns = [
                r'<div[^>]*alert-danger[^>]*>(.*?)</div>',
                r'<span[^>]*error[^>]*>(.*?)</span>',
                r'class="error[^"]*"[^>]*>(.*?)</',
            ]

            for pattern in error_patterns:
                matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                if matches:
                    for match in matches:
                        clean_error = re.sub(r'<[^>]+>', '', match).strip()
                        if clean_error:
                            print(f"❌ Erro encontrado: {clean_error}")

        print(f"Conteúdo (primeiros 500 chars): {response.text[:500]}")
        return None

def verificar_funcionario(session, funcionario_id):
    """Verificar dados do funcionário cadastrado"""
    print(f"🔍 Verificando funcionário ID: {funcionario_id}")
    
    detalhes_url = f"{BASE_URL}/funcionarios/{funcionario_id}"
    response = session.get(detalhes_url)
    
    if response.status_code == 200:
        # Procurar pelo status na página
        content = response.text
        
        if 'status-ativo' in content.lower() or 'badge-success' in content:
            print("✅ Status: ATIVO (encontrado na página)")
            return True
        elif 'status-inativo' in content.lower() or 'badge-danger' in content:
            print("❌ Status: INATIVO (encontrado na página)")
            return False
        else:
            print("⚠️ Status não identificado na página")
            # Procurar por texto "Ativo" ou "Inativo"
            if '>Ativo<' in content:
                print("✅ Status: ATIVO (texto encontrado)")
                return True
            elif '>Inativo<' in content:
                print("❌ Status: INATIVO (texto encontrado)")
                return False
            else:
                print("❓ Status não encontrado no HTML")
                return None
    else:
        print(f"❌ Erro ao acessar detalhes: {response.status_code}")
        return None

def limpar_funcionario_teste(session, funcionario_id):
    """Remover funcionário de teste"""
    print(f"🧹 Removendo funcionário de teste ID: {funcionario_id}")
    
    # Tentar deletar via API se existir
    delete_url = f"{BASE_URL}/funcionarios/{funcionario_id}/deletar"
    response = session.post(delete_url, allow_redirects=False)
    
    if response.status_code in [200, 302]:
        print("✅ Funcionário de teste removido")
    else:
        print(f"⚠️ Não foi possível remover automaticamente (status: {response.status_code})")
        print(f"   Remova manualmente o funcionário ID: {funcionario_id}")

def main():
    """Função principal"""
    print("🧪 TESTE AUTOMATIZADO: CADASTRO DE FUNCIONÁRIO")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"URL Base: {BASE_URL}")
    print()
    
    # Fazer login
    session = fazer_login()
    if not session:
        print("❌ Falha no login - teste abortado")
        return
    
    # Cadastrar funcionário
    funcionario_id = cadastrar_funcionario(session)
    if not funcionario_id:
        print("❌ Falha no cadastro - teste abortado")
        return
    
    # Verificar status
    status_ativo = verificar_funcionario(session, funcionario_id)
    
    # Resultado do teste
    print("\n" + "=" * 60)
    print("📊 RESULTADO DO TESTE:")
    
    if status_ativo is True:
        print("✅ SUCESSO: Funcionário cadastrado com status ATIVO")
        resultado = "PASSOU"
    elif status_ativo is False:
        print("❌ FALHA: Funcionário cadastrado com status INATIVO")
        resultado = "FALHOU"
    else:
        print("❓ INCONCLUSIVO: Não foi possível determinar o status")
        resultado = "INCONCLUSIVO"
    
    print(f"🎯 Resultado final: {resultado}")
    
    # Limpar dados de teste
    if funcionario_id:
        limpar_funcionario_teste(session, funcionario_id)
    
    print("\n✅ TESTE CONCLUÍDO")

if __name__ == "__main__":
    main()
