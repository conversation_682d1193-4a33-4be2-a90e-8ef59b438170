#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== LIMPANDO TODAS AS ALOCAÇÕES DE TESTE ===\n")
        
        # Verificar todas as alocações de teste
        testes = db.execute_query("""
            SELECT id, funcionario_id, observacoes, created_at
            FROM funcionario_alocacoes
            WHERE observacoes LIKE %s OR observacoes LIKE %s OR observacoes LIKE %s
        """, ('%TESTE%', '%teste%', '%correção%'))
        
        if testes:
            print(f"Encontradas {len(testes)} alocação(ões) de teste:")
            for t in testes:
                print(f"  - ID {t['id']}: Funcionário {t['funcionario_id']} - {t['observacoes']} ({t['created_at']})")
                db.execute_query("DELETE FROM funcionario_alocacoes WHERE id = %s", (t['id'],))
            print(f"✅ {len(testes)} alocação(ões) de teste removida(s)")
        else:
            print("✅ Nenhuma alocação de teste encontrada")
        
        # Verificar contagem final
        contagem = db.execute_query("""
            SELECT COUNT(DISTINCT funcionario_id) as count FROM (
                SELECT fa.funcionario_id
                FROM funcionario_alocacoes fa
                WHERE fa.empresa_cliente_id = 11 AND fa.ativo = TRUE

                UNION

                SELECT f.id as funcionario_id
                FROM funcionarios f
                WHERE f.empresa_id = 11 AND f.status_cadastro = 'Ativo'
            ) as todos_funcionarios
        """)
        
        total = contagem[0]['count'] if contagem else 0
        print(f"\n📊 Contagem final para AiNexus: {total} funcionário(s)")
        
        # Verificar todas as alocações restantes
        alocacoes_restantes = db.execute_query("""
            SELECT fa.id, fa.funcionario_id, f.nome_completo, fa.observacoes
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            WHERE fa.empresa_cliente_id = 11 AND fa.ativo = 1
        """)
        
        if alocacoes_restantes:
            print(f"\n📋 Alocações restantes para AiNexus:")
            for aloc in alocacoes_restantes:
                print(f"  - ID {aloc['id']}: {aloc['nome_completo']} - {aloc['observacoes'] or 'Sem observações'}")
        else:
            print(f"\n✅ Nenhuma alocação ativa restante")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
