-- Corrigir trigger que está causando erro na alocação
-- O problema é que o trigger não está fornecendo valor para o campo obrigatório dados_jornada_nova

DROP TRIGGER IF EXISTS tr_historico_alocacao_criada;

DELIMITER $$

CREATE TRIGGER tr_historico_alocacao_criada
AFTER INSERT ON funcionario_alocacoes
FOR EACH ROW
BEGIN
    
    INSERT INTO historico_funcionario (
        funcionario_id, tipo_evento, data_evento, data_referencia,
        detalhes, status_aprovacao
    ) VALUES (
        NEW.funcionario_id, 'ALOCACAO_CRIADA', NOW(), CURDATE(),
        CONCAT('Funcionário alocado para cliente ID ', NEW.empresa_cliente_id, 
               ' com jornada ID ', NEW.jornada_trabalho_id),
        'NAO_APLICAVEL'
    );
    
    -- Corrigir inserção no log_mudancas_jornada incluindo dados_jornada_nova
    INSERT INTO log_mudancas_jornada (
        funcionario_id, jornada_anterior_id, jornada_nova_id, 
        tipo_mudanca, motivo, dados_jornada_nova
    ) VALUES (
        NEW.funcionario_id, 
        (SELECT jornada_trabalho_id FROM funcionarios WHERE id = NEW.funcionario_id),
        NEW.jornada_trabalho_id, 
        'ALOCACAO_CLIENTE',
        CONCAT('Alocado para cliente - herdando jornada do cliente'),
        -- Buscar dados da nova jornada
        (SELECT JSON_OBJECT(
            'nome_jornada', j.nome_jornada,
            'seg_qui_entrada', j.seg_qui_entrada,
            'seg_qui_saida', j.seg_qui_saida,
            'sexta_entrada', j.sexta_entrada,
            'sexta_saida', j.sexta_saida,
            'intervalo_inicio', j.intervalo_inicio,
            'intervalo_fim', j.intervalo_fim,
            'tipo_jornada', j.tipo_jornada
        ) FROM jornadas_trabalho j WHERE j.id = NEW.jornada_trabalho_id)
    );
END$$

DELIMITER ;
