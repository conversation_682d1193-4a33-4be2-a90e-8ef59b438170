#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== CORREÇÃO DOS TIPOS DE JORNADA ===\n")
        
        # 1. Identificar jornadas com tipos incorretos
        print("1. 🔍 IDENTIFICANDO JORNADAS COM TIPOS INCORRETOS:")
        jornadas = db.execute_query("""
            SELECT id, nome_jornada, tipo_jornada, seg_qui_entrada, seg_qui_saida,
                   empresa_id
            FROM jornadas_trabalho 
            WHERE ativa = TRUE
        """)
        
        jornadas_para_corrigir = []
        
        for j in jornadas:
            entrada_hora = int(str(j['seg_qui_entrada']).split(':')[0])
            saida_hora = int(str(j['seg_qui_saida']).split(':')[0])
            
            # Determinar tipo correto baseado no horário
            if entrada_hora >= 6 and saida_hora <= 18:
                tipo_correto = "Diurno"
            elif entrada_hora >= 14 and saida_hora >= 22:
                tipo_correto = "Noturno"
            elif entrada_hora >= 22 or saida_hora <= 6:
                tipo_correto = "Noturno"  # Madrugada também é noturno
            else:
                tipo_correto = "Misto"
            
            if j['tipo_jornada'] != tipo_correto:
                jornadas_para_corrigir.append({
                    'id': j['id'],
                    'nome': j['nome_jornada'],
                    'tipo_atual': j['tipo_jornada'],
                    'tipo_correto': tipo_correto,
                    'horario': f"{j['seg_qui_entrada']}-{j['seg_qui_saida']}"
                })
                print(f"   ❌ ID {j['id']}: {j['nome_jornada']}")
                print(f"      Horário: {j['seg_qui_entrada']}-{j['seg_qui_saida']}")
                print(f"      Atual: {j['tipo_jornada']} → Correto: {tipo_correto}")
        
        if not jornadas_para_corrigir:
            print("   ✅ Todas as jornadas estão com tipos corretos!")
            return
        
        # 2. Confirmar correções
        print(f"\n2. 🔧 APLICANDO CORREÇÕES:")
        for jornada in jornadas_para_corrigir:
            print(f"   Corrigindo: {jornada['nome']} ({jornada['horario']})")
            print(f"   {jornada['tipo_atual']} → {jornada['tipo_correto']}")
            
            # Aplicar correção
            db.execute_query("""
                UPDATE jornadas_trabalho 
                SET tipo_jornada = %s 
                WHERE id = %s
            """, (jornada['tipo_correto'], jornada['id']))
            
            print(f"   ✅ Corrigido!")
        
        # 3. Verificar resultado
        print(f"\n3. ✅ VERIFICAÇÃO FINAL:")
        jornadas_verificacao = db.execute_query("""
            SELECT id, nome_jornada, tipo_jornada, seg_qui_entrada, seg_qui_saida
            FROM jornadas_trabalho 
            WHERE id IN ({})
        """.format(','.join([str(j['id']) for j in jornadas_para_corrigir])))
        
        for jv in jornadas_verificacao:
            print(f"   ID {jv['id']}: {jv['nome_jornada']}")
            print(f"   Horário: {jv['seg_qui_entrada']}-{jv['seg_qui_saida']}")
            print(f"   Tipo: {jv['tipo_jornada']}")
            print()
        
        print(f"🎉 CORREÇÃO CONCLUÍDA! {len(jornadas_para_corrigir)} jornadas corrigidas.")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
