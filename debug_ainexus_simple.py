#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== INVESTIGAÇÃO AINEXUS - PROBLEMA JORNADAS ===\n")
        
        # 1. Confirmar dados da AiNexus
        print("1. 🏢 EMPRESA AINEXUS:")
        ainexus = db.execute_query("SELECT id, razao_social, empresa_principal FROM empresas WHERE razao_social LIKE %s", ('%AiNexus%',))[0]
        print(f"   ID: {ainexus['id']} | Nome: {ainexus['razao_social']} | Principal: {ainexus['empresa_principal']}")
        
        # 2. Verificar jornadas ativas
        print(f"\n2. ⏰ JORNADAS ATIVAS DA AINEXUS:")
        jornadas = db.execute_query("""
            SELECT id, nome_jornada, ativa, padrao, empresa_id
            FROM jornadas_trabalho 
            WHERE empresa_id = %s AND ativa = TRUE
            ORDER BY padrao DESC, nome_jornada
        """, (ainexus['id'],))
        
        for j in jornadas:
            print(f"   ✅ ID: {j['id']} | {j['nome_jornada']} | Padrão: {j['padrao']}")
        
        # 3. Testar API exata
        print(f"\n3. 🧪 TESTE API EXATA (empresa_id = {ainexus['id']}):")
        api_result = db.execute_query("""
            SELECT j.id, j.nome_jornada as nome, j.ativa, e.razao_social as empresa_nome
            FROM jornadas_trabalho j
            JOIN empresas e ON j.empresa_id = e.id
            WHERE j.ativa = TRUE AND j.empresa_id = %s
            ORDER BY j.padrao DESC, j.nome_jornada
        """, (ainexus['id'],))
        
        if api_result:
            print(f"   ✅ API retornaria {len(api_result)} jornadas:")
            for r in api_result:
                print(f"      - ID: {r['id']} | {r['nome']} | Empresa: {r['empresa_nome']}")
        else:
            print("   ❌ API não retornaria nenhuma jornada!")
        
        # 4. Verificar se AiNexus é cliente
        print(f"\n4. 🤝 AINEXUS COMO CLIENTE:")
        clientes = db.execute_query("""
            SELECT empresa_principal_id, empresa_cliente_id
            FROM empresa_clientes 
            WHERE empresa_cliente_id = %s
        """, (ainexus['id'],))
        
        if clientes:
            for c in clientes:
                print(f"   ✅ AiNexus é cliente da empresa ID: {c['empresa_principal_id']}")
        else:
            print("   ❌ AiNexus NÃO é cliente de nenhuma empresa!")
        
        # 5. Verificar estrutura da tabela empresa_clientes
        print(f"\n5. 📋 ESTRUTURA TABELA empresa_clientes:")
        estrutura = db.execute_query("DESCRIBE empresa_clientes")
        for campo in estrutura:
            print(f"   - {campo['Field']}: {campo['Type']}")
        
        # 6. Verificar todos os clientes
        print(f"\n6. 👥 TODOS OS CLIENTES CADASTRADOS:")
        todos_clientes = db.execute_query("""
            SELECT ec.empresa_cliente_id, e.razao_social
            FROM empresa_clientes ec
            JOIN empresas e ON ec.empresa_cliente_id = e.id
        """)
        
        for tc in todos_clientes:
            print(f"   - ID: {tc['empresa_cliente_id']} | {tc['razao_social']}")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
