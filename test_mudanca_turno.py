#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_mudanca_turno():
    """Testar funcionalidade de mudança de turno"""
    
    print("=== TESTE MUDANÇA DE TURNO ===\n")
    
    # Fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    login_response = session.post('http://************/login', data=login_data)
    
    print(f"1. Login status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ Falha no login")
        return
    
    # Testar nova API que permite múltiplas alocações
    print(f"\n2. Testando API de funcionários (permite múltiplas jornadas):")
    api_response = session.get('http://************/empresa-principal/api/funcionarios-disponiveis/11')
    
    if api_response.status_code == 200:
        data = api_response.json()
        print(f"   ✅ Status: {api_response.status_code}")
        print(f"   Success: {data.get('success')}")
        print(f"   Total funcionários: {len(data.get('funcionarios', []))}")
        
        funcionarios = data.get('funcionarios', [])
        for func in funcionarios:
            status_alocacao = "JÁ ALOCADO" if func.get('ja_alocado') else "DISPONÍVEL"
            jornadas = func.get('jornadas_alocadas') or "Nenhuma"
            print(f"     - {func['nome_completo']} | Status: {status_alocacao} | Jornadas: {jornadas}")
    else:
        print(f"   ❌ Erro: {api_response.status_code}")
        print(f"   Resposta: {api_response.text[:200]}")
    
    # Testar API de buscar alocação específica
    print(f"\n3. Testando API de buscar alocação (funcionário TESTE MOV 2 - ID 72):")
    alocacao_response = session.get('http://************/empresa-principal/api/alocacao-funcionario/72')
    
    if alocacao_response.status_code == 200:
        alocacao_data = alocacao_response.json()
        print(f"   ✅ Status: {alocacao_response.status_code}")
        print(f"   Success: {alocacao_data.get('success')}")
        
        if alocacao_data.get('success'):
            alocacao = alocacao_data.get('alocacao')
            print(f"   Alocação ID: {alocacao['id']}")
            print(f"   Funcionário: {alocacao['funcionario_nome']}")
            print(f"   Cliente: {alocacao['empresa_nome']}")
            print(f"   Jornada: {alocacao['nome_jornada']} ({alocacao['tipo_jornada']})")
            print(f"   Cargo: {alocacao['cargo_no_cliente']}")
            print(f"   Período: {alocacao['data_inicio']} até {alocacao['data_fim'] or 'Indefinido'}")
        else:
            print(f"   ❌ Erro: {alocacao_data.get('message')}")
    else:
        print(f"   ❌ Erro: {alocacao_response.status_code}")
    
    print(f"\n4. 📋 CENÁRIOS DE MUDANÇA DE TURNO:")
    print(f"   ✅ Funcionário pode ser alocado múltiplas vezes para o mesmo cliente")
    print(f"   ✅ Cada alocação pode ter jornada diferente (Primeiro Turno, Segundo Turno)")
    print(f"   ✅ Sistema mostra jornadas já alocadas para contexto")
    print(f"   ✅ Botão de editar permite alterar jornada da alocação existente")
    
    print(f"\n5. 🎯 PRÓXIMOS PASSOS:")
    print(f"   1. Testar interface: abrir modal de alocação")
    print(f"   2. Verificar se funcionários já alocados aparecem com contexto")
    print(f"   3. Testar botão de editar alocação (amarelo)")
    print(f"   4. Alocar mesmo funcionário com jornada diferente")

if __name__ == "__main__":
    test_mudanca_turno()
