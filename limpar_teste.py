#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== LIMPANDO ALOCAÇÃO DE TESTE ===\n")
        
        # Verificar se existe a alocação de teste
        teste = db.execute_query("""
            SELECT id, funcionario_id, observacoes 
            FROM funcionario_alocacoes 
            WHERE observacoes = 'Teste de deploy'
        """)
        
        if teste:
            for t in teste:
                print(f"Removendo alocação ID {t['id']} (Funcionário {t['funcionario_id']})")
                db.execute_query("DELETE FROM funcionario_alocacoes WHERE id = %s", (t['id'],))
            print(f"✅ {len(teste)} alocação(ões) de teste removida(s)")
        else:
            print("✅ Nenhuma alocação de teste encontrada")
        
        # Verificar contagem final
        contagem = db.execute_query("""
            SELECT COUNT(DISTINCT funcionario_id) as count FROM (
                SELECT fa.funcionario_id
                FROM funcionario_alocacoes fa
                WHERE fa.empresa_cliente_id = 11 AND fa.ativo = TRUE

                UNION

                SELECT f.id as funcionario_id
                FROM funcionarios f
                WHERE f.empresa_id = 11 AND f.status_cadastro = 'Ativo'
            ) as todos_funcionarios
        """)
        
        total = contagem[0]['count'] if contagem else 0
        print(f"\n📊 Contagem final para AiNexus: {total} funcionário(s)")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
