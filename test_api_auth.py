#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_api_with_auth():
    """Testar API com autenticação"""
    
    # Criar sessão
    session = requests.Session()
    
    # 1. Fazer login
    print("1. 🔐 FAZENDO LOGIN...")
    login_data = {
        'username': 'admin',
        'password': '@Ric6109'
    }
    
    login_response = session.post('http://************/login', data=login_data)
    print(f"   Status login: {login_response.status_code}")
    
    if login_response.status_code == 200:
        print("   ✅ Login realizado com sucesso!")
    else:
        print("   ❌ Falha no login!")
        return
    
    # 2. Testar sessão
    print("\n2. 🧪 TESTANDO SESSÃO...")
    session_url = 'http://************/empresa-principal/api/test-session'
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }

    session_response = session.get(session_url, headers=headers)
    print(f"   Status sessão: {session_response.status_code}")
    if session_response.status_code == 200:
        print(f"   Dados sessão: {session_response.json()}")
    else:
        print(f"   Erro sessão: {session_response.text}")

    # 3. Testar API de jornadas
    print("\n3. 🧪 TESTANDO API JORNADAS...")
    api_url = 'http://************/empresa-principal/api/jornadas-empresa/11'
    print(f"   URL: {api_url}")

    api_response = session.get(api_url, headers=headers)
    print(f"   Status API: {api_response.status_code}")
    print(f"   Headers: {dict(api_response.headers)}")
    
    if api_response.status_code == 200:
        try:
            data = api_response.json()
            print("   ✅ API retornou JSON válido:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"   ❌ Erro ao parsear JSON: {e}")
            print(f"   Conteúdo bruto: {api_response.text[:500]}")
    else:
        print(f"   ❌ API retornou erro {api_response.status_code}")
        print(f"   Conteúdo: {api_response.text[:500]}")

if __name__ == "__main__":
    test_api_with_auth()
