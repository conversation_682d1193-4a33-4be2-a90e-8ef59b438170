#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste Real - Verificar problema do status_cadastro
Sistema: RLPONTO-WEB
Data: 20/07/2025
"""

import sys
import os
import pymysql
from datetime import datetime

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def conectar_banco():
    """Conectar ao banco de dados"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Conexão com banco estabelecida")
        return connection
    except Exception as e:
        print(f"❌ Erro ao conectar: {e}")
        return None

def testar_insercao_direta():
    """Testar inserção direta simulando o código da aplicação"""
    print("\n🧪 TESTE: INSERÇÃO DIRETA SIMULANDO APLICAÇÃO")
    print("=" * 60)
    
    conn = conectar_banco()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        timestamp = datetime.now().strftime("%H%M%S")
        
        # Dados exatamente como a aplicação envia
        dados_teste = {
            'nome_completo': f'TESTE STATUS REAL {timestamp}',
            'cpf': '000.000.000-00',
            'rg': f'TSR-{timestamp}',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'Solteiro',
            'nacionalidade': 'Brasileira',
            'ctps_numero': None,
            'ctps_serie_uf': None,
            'pis_pasep': '000.00000.00-0',
            'endereco_rua': 'Rua Teste',
            'endereco_bairro': 'Bairro Teste',
            'endereco_cidade': 'Cidade Teste',
            'endereco_cep': '00000-000',
            'endereco_estado': 'SP',
            'telefone1': '(11) 99999-9999',
            'telefone2': None,
            'email': None,
            'cargo': 'TESTE STATUS',
            'setor_obra': 'TESTE',
            'matricula_empresa': f'TSR{timestamp}',
            'data_admissao': datetime.now().strftime('%Y-%m-%d'),
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'Funcionario',
            'turno': 'Diurno',
            'tolerancia_ponto': 10,
            'banco_horas': 0,
            'hora_extra': 0,
            'status_cadastro': 'Ativo',  # EXPLICITAMENTE ATIVO
            'horas_semanais_obrigatorias': 44.00,
            'empresa_id': 4,
            'jornada_trabalho_id': None,
            'digital_dedo1': None,
            'digital_dedo2': None,
            'foto_3x4': None
        }
        
        print(f"📝 Dados para inserção:")
        print(f"   Nome: {dados_teste['nome_completo']}")
        print(f"   Status: {dados_teste['status_cadastro']}")
        print(f"   Empresa ID: {dados_teste['empresa_id']}")
        print()
        
        # Query EXATA da aplicação
        sql = """
        INSERT INTO funcionarios (
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
            horas_semanais_obrigatorias, empresa_id, jornada_trabalho_id,
            digital_dedo1, digital_dedo2, foto_3x4
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s
        )
        """
        
        # Parâmetros EXATOS da aplicação
        params = (
            dados_teste['nome_completo'], dados_teste['cpf'], dados_teste['rg'], dados_teste['data_nascimento'],
            dados_teste['sexo'], dados_teste['estado_civil'], dados_teste['nacionalidade'],
            dados_teste['ctps_numero'], dados_teste['ctps_serie_uf'], dados_teste['pis_pasep'],
            dados_teste['endereco_rua'], dados_teste['endereco_bairro'], dados_teste['endereco_cidade'], 
            dados_teste['endereco_cep'], dados_teste['endereco_estado'],
            dados_teste['telefone1'], dados_teste['telefone2'], dados_teste['email'],
            dados_teste['cargo'], dados_teste['setor_obra'], dados_teste['matricula_empresa'],
            dados_teste['data_admissao'], dados_teste['tipo_contrato'],
            dados_teste['nivel_acesso'], dados_teste['turno'], dados_teste['tolerancia_ponto'],
            dados_teste['banco_horas'], dados_teste['hora_extra'], dados_teste['status_cadastro'],
            dados_teste['horas_semanais_obrigatorias'], dados_teste['empresa_id'], dados_teste['jornada_trabalho_id'],
            dados_teste['digital_dedo1'], dados_teste['digital_dedo2'], dados_teste['foto_3x4']
        )
        
        print(f"🔍 Executando query com status_cadastro = '{dados_teste['status_cadastro']}'")
        
        cursor.execute(sql, params)
        funcionario_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Funcionário inserido com ID: {funcionario_id}")
        
        # Verificar IMEDIATAMENTE após inserção
        sql_verificar = """
        SELECT id, nome_completo, status_cadastro, data_cadastro 
        FROM funcionarios 
        WHERE id = %s
        """
        cursor.execute(sql_verificar, (funcionario_id,))
        resultado = cursor.fetchone()
        
        if resultado:
            print(f"\n🔍 VERIFICAÇÃO IMEDIATA:")
            print(f"   ID: {resultado['id']}")
            print(f"   Nome: {resultado['nome_completo']}")
            print(f"   Status: {resultado['status_cadastro']}")
            print(f"   Data Cadastro: {resultado['data_cadastro']}")
            
            if resultado['status_cadastro'] == 'Ativo':
                print("✅ STATUS CORRETO: Ativo")
                status_ok = True
            else:
                print(f"❌ STATUS INCORRETO: {resultado['status_cadastro']} (esperado: Ativo)")
                status_ok = False
        else:
            print("❌ Funcionário não encontrado após inserção!")
            status_ok = False
        
        # Aguardar um pouco e verificar novamente
        print(f"\n⏳ Aguardando 2 segundos...")
        import time
        time.sleep(2)
        
        cursor.execute(sql_verificar, (funcionario_id,))
        resultado2 = cursor.fetchone()
        
        if resultado2:
            print(f"\n🔍 VERIFICAÇÃO APÓS 2 SEGUNDOS:")
            print(f"   Status: {resultado2['status_cadastro']}")
            
            if resultado2['status_cadastro'] != resultado['status_cadastro']:
                print(f"🚨 STATUS MUDOU! De '{resultado['status_cadastro']}' para '{resultado2['status_cadastro']}'")
            else:
                print(f"✅ Status mantido: {resultado2['status_cadastro']}")
        
        # Limpar dados de teste
        print(f"\n🧹 Removendo dados de teste...")
        cursor.execute("DELETE FROM funcionarios WHERE id = %s", (funcionario_id,))
        conn.commit()
        print("✅ Dados de teste removidos")
        
        return status_ok
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verificar_funcionarios_recentes():
    """Verificar status dos funcionários cadastrados recentemente"""
    print("\n🔍 VERIFICANDO FUNCIONÁRIOS RECENTES")
    print("=" * 60)
    
    conn = conectar_banco()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Buscar últimos 5 funcionários
        sql = """
        SELECT id, nome_completo, status_cadastro, data_cadastro
        FROM funcionarios 
        ORDER BY data_cadastro DESC 
        LIMIT 5
        """
        
        cursor.execute(sql)
        funcionarios = cursor.fetchall()
        
        print(f"📊 Últimos {len(funcionarios)} funcionários cadastrados:")
        print()
        
        ativos = 0
        inativos = 0
        
        for func in funcionarios:
            status_icon = "✅" if func['status_cadastro'] == 'Ativo' else "❌"
            print(f"{status_icon} ID: {func['id']} | {func['nome_completo']} | Status: {func['status_cadastro']} | Data: {func['data_cadastro']}")
            
            if func['status_cadastro'] == 'Ativo':
                ativos += 1
            else:
                inativos += 1
        
        print(f"\n📈 RESUMO:")
        print(f"   ✅ Ativos: {ativos}")
        print(f"   ❌ Inativos: {inativos}")
        
        if inativos > 0:
            print(f"\n🚨 PROBLEMA CONFIRMADO: {inativos} funcionários recentes estão inativos!")
        else:
            print(f"\n✅ Todos os funcionários recentes estão ativos")
        
    except Exception as e:
        print(f"❌ Erro: {e}")
    finally:
        conn.close()

def main():
    """Função principal"""
    print("🔍 TESTE REAL: INVESTIGAÇÃO DO STATUS_CADASTRO")
    print("=" * 70)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Verificar funcionários recentes
    verificar_funcionarios_recentes()
    
    # Testar inserção
    status_ok = testar_insercao_direta()
    
    print("\n" + "=" * 70)
    print("📊 RESULTADO FINAL:")
    
    if status_ok:
        print("✅ TESTE PASSOU: Inserção direta funciona corretamente")
        print("💡 CONCLUSÃO: O problema pode estar no formulário web ou validação")
    else:
        print("❌ TESTE FALHOU: Problema na inserção direta")
        print("💡 CONCLUSÃO: O problema está no banco de dados ou triggers")
    
    print("\n✅ TESTE CONCLUÍDO")

if __name__ == "__main__":
    main()
