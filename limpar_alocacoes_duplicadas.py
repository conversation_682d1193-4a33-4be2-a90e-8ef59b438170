#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== LIMPEZA DE ALOCAÇÕES DUPLICADAS ===\n")
        
        # 1. Identificar alocações duplicadas
        print("1. 🔍 IDENTIFICANDO ALOCAÇÕES DUPLICADAS:")
        
        duplicadas = db.execute_query("""
            SELECT funcionario_id, empresa_cliente_id, COUNT(*) as total,
                   GROUP_CONCAT(id ORDER BY id) as ids,
                   GROUP_CONCAT(created_at ORDER BY id) as datas
            FROM funcionario_alocacoes 
            WHERE ativo = 1
            GROUP BY funcionario_id, empresa_cliente_id
            HAVING COUNT(*) > 1
        """)
        
        if duplicadas:
            print(f"   Encontradas {len(duplicadas)} duplicação(ões):")
            for dup in duplicadas:
                print(f"     Funcionário {dup['funcionario_id']} → Cliente {dup['empresa_cliente_id']}: {dup['total']} alocações")
                print(f"       IDs: {dup['ids']}")
                print(f"       Datas: {dup['datas']}")
        else:
            print("   ✅ Nenhuma duplicação encontrada")
            return
        
        # 2. Para cada duplicação, manter apenas a mais recente
        print(f"\n2. 🧹 LIMPANDO DUPLICAÇÕES:")
        
        for dup in duplicadas:
            ids = dup['ids'].split(',')
            ids_para_remover = ids[:-1]  # Remover todos exceto o último (mais recente)
            
            print(f"   Funcionário {dup['funcionario_id']} → Cliente {dup['empresa_cliente_id']}:")
            print(f"     Mantendo alocação ID: {ids[-1]} (mais recente)")
            print(f"     Removendo alocações: {', '.join(ids_para_remover)}")
            
            for id_remover in ids_para_remover:
                db.execute_query("DELETE FROM funcionario_alocacoes WHERE id = %s", (int(id_remover),))
                print(f"       ✅ Removida alocação ID {id_remover}")
        
        # 3. Verificar resultado
        print(f"\n3. ✅ VERIFICAÇÃO FINAL:")
        
        verificacao = db.execute_query("""
            SELECT funcionario_id, empresa_cliente_id, COUNT(*) as total
            FROM funcionario_alocacoes 
            WHERE ativo = 1
            GROUP BY funcionario_id, empresa_cliente_id
            HAVING COUNT(*) > 1
        """)
        
        if verificacao:
            print(f"   ❌ Ainda há {len(verificacao)} duplicação(ões)")
        else:
            print(f"   ✅ Todas as duplicações foram removidas")
        
        # 4. Mostrar alocações restantes
        print(f"\n4. 📋 ALOCAÇÕES ATIVAS RESTANTES:")
        
        alocacoes_restantes = db.execute_query("""
            SELECT fa.id, fa.funcionario_id, f.nome_completo, fa.empresa_cliente_id, e.razao_social,
                   fa.data_inicio, fa.data_fim
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            WHERE fa.ativo = 1
            ORDER BY f.nome_completo, e.razao_social
        """)
        
        if alocacoes_restantes:
            for aloc in alocacoes_restantes:
                print(f"   ID {aloc['id']}: {aloc['nome_completo']} → {aloc['razao_social']} ({aloc['data_inicio']} até {aloc['data_fim'] or 'Em aberto'})")
        else:
            print(f"   ✅ Nenhuma alocação ativa")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
