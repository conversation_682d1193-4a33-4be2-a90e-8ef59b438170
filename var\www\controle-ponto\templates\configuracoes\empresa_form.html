{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .header-card {
        background: linear-gradient(135deg, #6f42c1, #5a32a3);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(111,66,193,0.3);
    }
    
    .header-card h2 {
        margin: 0;
        font-size: 2rem;
    }
    
    .form-card {
        background: white;
        border-radius: 15px;
        padding: 40px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #495057;
    }
    
    .form-group label .required {
        color: #dc3545;
        margin-left: 3px;
    }
    
    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }
    
    .form-control:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111,66,193,0.25);
        outline: none;
    }
    
    .form-control.is-invalid {
        border-color: #dc3545;
    }
    
    .form-control.is-valid {
        border-color: #28a745;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .form-check {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    
    .form-check input[type="checkbox"] {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        cursor: pointer;
    }
    
    .form-check label {
        margin: 0;
        cursor: pointer;
        font-weight: normal;
    }
    
    .input-group {
        position: relative;
    }
    
    .input-group .form-control {
        padding-left: 45px;
    }
    
    .input-group .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 5;
    }
    
    .validation-feedback {
        display: block;
        margin-top: 5px;
        font-size: 0.875rem;
        padding: 5px 10px;
        border-radius: 4px;
    }
    
    .validation-feedback.invalid {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .validation-feedback.valid {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 40px;
        padding-top: 30px;
        border-top: 1px solid #e9ecef;
    }
    
    .btn {
        padding: 12px 30px;
        border-radius: 8px;
        border: none;
        cursor: pointer;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        transition: all 0.3s ease;
        min-width: 120px;
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #6f42c1, #5a32a3);
        color: white;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(111,66,193,0.3);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #545b62;
        color: white;
    }
    
    .loading {
        display: none;
        align-items: center;
        justify-content: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 9999;
    }
    
    .loading-content {
        background: white;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        max-width: 300px;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #6f42c1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .form-help {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 25px;
    }
    
    .form-help h6 {
        margin: 0 0 10px 0;
        color: #1976d2;
    }
    
    .form-help ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .form-help li {
        margin: 5px 0;
        color: #424242;
    }
    
    /* Estilos para Horário Padrão */
    .horario-padrao-card {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 12px !important;
        padding: 25px !important;
        margin-top: 10px !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .horario-periodo h6 {
        color: #495057 !important;
        margin-bottom: 20px !important;
        font-weight: 600 !important;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1.1rem;
    }

    .horario-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        margin-bottom: 15px;
    }

    .horario-item {
        display: flex;
        flex-direction: column;
    }

    .form-label-small {
        font-size: 0.875rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: block;
        text-align: center;
    }

    .horario-item .form-control {
        text-align: center;
        font-weight: 500;
        font-size: 0.95rem;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .horario-item .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    .horario-preview {
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 16px;
        margin-top: 15px;
        text-align: center;
    }

    .horario-display {
        font-family: 'Courier New', monospace;
        font-size: 1rem;
        font-weight: 600;
        color: #495057;
    }

    .config-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        align-items: center;
    }

    .config-item {
        display: flex;
        flex-direction: column;
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px;
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        margin: 0;
    }

    .form-check-label {
        font-size: 0.95rem;
        color: #495057;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .input-group-text {
        font-size: 0.875rem;
        background-color: #e9ecef;
        border-color: #ced4da;
        font-weight: 500;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .btn {
            width: 100%;
        }

        .horario-padrao-card {
            padding: 20px 15px !important;
        }

        .horario-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .config-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .horario-periodo h6 {
            font-size: 1rem;
        }

        .form-label-small {
            font-size: 0.8rem;
        }

        .horario-item .form-control {
            font-size: 0.9rem;
        }

        .horario-display {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 480px) {
        .horario-grid {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .horario-padrao-card {
            padding: 15px 10px !important;
        }

        .horario-preview {
            padding: 10px 12px;
        }

        .horario-display {
            font-size: 0.85rem;
            word-break: break-all;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <!-- Header -->
    <div class="header-card">
        <h2>
            <i class="fas fa-building"></i> {{ titulo }}
        </h2>
    </div>

    <!-- Formulário -->
    <div class="form-card">
        <div class="form-help">
            <h6><i class="fas fa-info-circle"></i> Informações Importantes</h6>
            <ul>
                <li>Campos marcados com * são obrigatórios</li>
                <li>O CNPJ será validado automaticamente</li>
                <li>Empresas inativas não aparecerão na seleção de funcionários</li>
            </ul>
        </div>

        <form id="form-empresa" method="POST" enctype="multipart/form-data" novalidate>
            <!-- Dados Básicos -->
            <div class="form-group">
                <label for="razao_social">
                    <i class="fas fa-building"></i> Razão Social
                    <span class="required">*</span>
                </label>
                <input type="text"
                       id="razao_social"
                       name="razao_social"
                       class="form-control"
                       value="{{ dados.razao_social }}"
                       required
                       maxlength="200"
                       placeholder="Digite a razão social da empresa">
                <div id="razao_social-feedback" class="validation-feedback"></div>
            </div>

            <div class="form-group">
                <label for="nome_fantasia">
                    <i class="fas fa-tag"></i> Nome Fantasia
                </label>
                <input type="text"
                       id="nome_fantasia"
                       name="nome_fantasia"
                       class="form-control"
                       value="{{ dados.nome_fantasia }}"
                       maxlength="200"
                       placeholder="Digite o nome fantasia (opcional)">
            </div>

            <div class="form-group">
                <label for="cnpj">
                    <i class="fas fa-id-card"></i> CNPJ
                    <span class="required">*</span>
                </label>
                <div class="input-group">
                    <input type="text" 
                           id="cnpj" 
                           name="cnpj" 
                           class="form-control" 
                           value="{{ dados.cnpj }}"
                           required
                           placeholder="00.000.000/0000-00"
                           maxlength="18">
                </div>
                <div id="cnpj-feedback" class="validation-feedback"></div>
            </div>

            <!-- Contato -->
            <div class="form-row">
                <div class="form-group">
                    <label for="telefone">
                        <i class="fas fa-phone"></i> Telefone
                    </label>
                    <input type="tel" 
                           id="telefone" 
                           name="telefone" 
                           class="form-control" 
                           value="{{ dados.telefone }}"
                           placeholder="(00) 0000-0000"
                           maxlength="15">
                </div>

                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i> Email
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-control" 
                           value="{{ dados.email }}"
                           placeholder="<EMAIL>"
                           maxlength="100">
                    <div id="email-feedback" class="validation-feedback"></div>
                </div>
            </div>

            <!-- Logo da Empresa -->
            <div class="form-group">
                <label for="logotipo">
                    <i class="fas fa-image"></i> Logo da Empresa
                </label>
                <input type="file"
                       id="logotipo"
                       name="logotipo"
                       class="form-control"
                       accept="image/*">
                <small class="text-muted">Formatos aceitos: JPG, PNG, GIF. Tamanho máximo: 2MB</small>
                {% if dados.logotipo %}
                <div class="mt-2">
                    <img src="{{ url_for('configuracoes.logo_empresa', empresa_id=empresa_id) }}"
                         alt="Logo atual"
                         style="max-width: 150px; max-height: 100px; border: 1px solid #ddd; border-radius: 4px;">
                    <small class="text-muted d-block">Logo atual</small>
                </div>
                {% endif %}
            </div>

            <!-- Horário Padrão Aplicado -->
            <div class="form-group">
                <label>
                    <i class="fas fa-clock"></i> Horário Padrão Aplicado
                    <span class="required">*</span>
                </label>

                <!-- Card de Horário Padrão -->
                <div class="horario-padrao-card" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin-top: 10px;">

                    <!-- Segunda a Quinta -->
                    <div class="horario-periodo">
                        <h6 style="color: #495057; margin-bottom: 15px; font-weight: 600;">
                            <i class="fas fa-calendar-week"></i> Segunda a Quinta:
                        </h6>
                        <div class="horario-grid">
                            <div class="horario-item">
                                <label class="form-label-small">Entrada</label>
                                <input type="time"
                                       name="jornada_segunda_entrada"
                                       class="form-control"
                                       value="{{ dados.jornada_segunda_entrada or '08:00' }}"
                                       required>
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída Intervalo</label>
                                <input type="time"
                                       name="jornada_segunda_saida_almoco"
                                       class="form-control"
                                       value="{{ dados.jornada_segunda_saida_almoco or '12:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Retorno Intervalo</label>
                                <input type="time"
                                       name="jornada_segunda_entrada_almoco"
                                       class="form-control"
                                       value="{{ dados.jornada_segunda_entrada_almoco or '13:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída</label>
                                <input type="time"
                                       name="jornada_segunda_saida"
                                       class="form-control"
                                       value="{{ dados.jornada_segunda_saida or '17:00' }}"
                                       required>
                            </div>
                        </div>

                        <!-- Visualização dos horários -->
                        <div class="horario-preview">
                            <span class="horario-display" id="seg-qui-display">
                                <span id="seg-entrada">08:00</span> - <span id="seg-saida-almoco">12:00</span> /
                                <span id="seg-entrada-almoco">13:00</span> - <span id="seg-saida">17:00</span>
                            </span>
                        </div>
                    </div>

                    <!-- Sexta-feira -->
                    <div class="horario-periodo" style="margin-top: 25px;">
                        <h6 style="color: #495057; margin-bottom: 15px; font-weight: 600;">
                            <i class="fas fa-calendar-day"></i> Sexta-feira:
                        </h6>
                        <div class="horario-grid">
                            <div class="horario-item">
                                <label class="form-label-small">Entrada</label>
                                <input type="time"
                                       name="jornada_sexta_entrada"
                                       class="form-control"
                                       value="{{ dados.jornada_sexta_entrada or '08:00' }}"
                                       required>
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída Intervalo</label>
                                <input type="time"
                                       name="jornada_sexta_saida_almoco"
                                       class="form-control"
                                       value="{{ dados.jornada_sexta_saida_almoco or '12:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Retorno Intervalo</label>
                                <input type="time"
                                       name="jornada_sexta_entrada_almoco"
                                       class="form-control"
                                       value="{{ dados.jornada_sexta_entrada_almoco or '13:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída</label>
                                <input type="time"
                                       name="jornada_sexta_saida"
                                       class="form-control"
                                       value="{{ dados.jornada_sexta_saida or '16:30' }}"
                                       required>
                            </div>
                        </div>

                        <!-- Visualização dos horários -->
                        <div class="horario-preview">
                            <span class="horario-display" id="sexta-display">
                                <span id="sexta-entrada">08:00</span> - <span id="sexta-saida-almoco">12:00</span> /
                                <span id="sexta-entrada-almoco">13:00</span> - <span id="sexta-saida">16:30</span>
                            </span>
                        </div>
                    </div>

                    <!-- Configurações Adicionais -->
                    <div class="horario-config" style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                        <div class="config-grid">
                            <div class="config-item">
                                <div class="form-check">
                                    <input type="checkbox"
                                           id="intervalo_obrigatorio"
                                           name="intervalo_obrigatorio"
                                           value="1"
                                           {{ 'checked' if dados.intervalo_obrigatorio else '' }}
                                           class="form-check-input">
                                    <label for="intervalo_obrigatorio" class="form-check-label">
                                        <i class="fas fa-clock text-primary"></i>
                                        <strong>Intervalo:</strong> Obrigatório (1h)
                                    </label>
                                </div>
                            </div>
                            <div class="config-item">
                                <label class="form-label-small">
                                    <i class="fas fa-stopwatch text-warning"></i>
                                    Tolerância Empresa
                                </label>
                                <div class="input-group">
                                    <input type="number"
                                           name="tolerancia_empresa_minutos"
                                           class="form-control"
                                           value="{{ dados.tolerancia_empresa_minutos or 15 }}"
                                           min="0" max="60"
                                           id="tolerancia-input">
                                    <div class="input-group-append">
                                        <span class="input-group-text">minutos</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    Funcionários desta empresa herdarão automaticamente estes horários.
                </small>
            </div>

            <!-- ========================================== -->
            <!-- SEGUNDO TURNO - NOVA IMPLEMENTAÇÃO -->
            <!-- ========================================== -->
            <div class="form-group">
                <label>
                    <i class="fas fa-moon"></i> Segundo Turno (Vespertino/Noturno)
                    <span class="text-muted">(Opcional)</span>
                </label>

                <!-- Card de Segundo Turno -->
                <div class="horario-padrao-card" style="background: #f1f3f4; border: 1px solid #d1d5db; border-radius: 8px; padding: 20px; margin-top: 10px;">

                    <!-- Segunda a Quinta -->
                    <div class="horario-periodo">
                        <h6 style="color: #495057; margin-bottom: 15px; font-weight: 600;">
                            <i class="fas fa-calendar-week"></i> Segunda a Quinta:
                        </h6>
                        <div class="horario-grid">
                            <div class="horario-item">
                                <label class="form-label-small">Entrada</label>
                                <input type="time"
                                       name="segundo_turno_segunda_entrada"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_segunda_entrada or '14:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída Intervalo</label>
                                <input type="time"
                                       name="segundo_turno_segunda_saida_jantar"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_segunda_saida_jantar or '18:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Retorno Intervalo</label>
                                <input type="time"
                                       name="segundo_turno_segunda_entrada_jantar"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_segunda_entrada_jantar or '19:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída</label>
                                <input type="time"
                                       name="segundo_turno_segunda_saida"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_segunda_saida or '22:00' }}">
                            </div>
                        </div>

                        <!-- Visualização dos horários -->
                        <div class="horario-preview">
                            <span class="horario-display" id="segundo-seg-qui-display">
                                <span id="segundo-seg-entrada">14:00</span> - <span id="segundo-seg-saida-jantar">18:00</span> /
                                <span id="segundo-seg-entrada-jantar">19:00</span> - <span id="segundo-seg-saida">22:00</span>
                            </span>
                        </div>
                    </div>

                    <!-- Sexta-feira -->
                    <div class="horario-periodo" style="margin-top: 25px;">
                        <h6 style="color: #495057; margin-bottom: 15px; font-weight: 600;">
                            <i class="fas fa-calendar-day"></i> Sexta-feira:
                        </h6>
                        <div class="horario-grid">
                            <div class="horario-item">
                                <label class="form-label-small">Entrada</label>
                                <input type="time"
                                       name="segundo_turno_sexta_entrada"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_sexta_entrada or '14:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída Intervalo</label>
                                <input type="time"
                                       name="segundo_turno_sexta_saida_jantar"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_sexta_saida_jantar or '18:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Retorno Intervalo</label>
                                <input type="time"
                                       name="segundo_turno_sexta_entrada_jantar"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_sexta_entrada_jantar or '19:00' }}">
                            </div>
                            <div class="horario-item">
                                <label class="form-label-small">Saída</label>
                                <input type="time"
                                       name="segundo_turno_sexta_saida"
                                       class="form-control"
                                       value="{{ dados.segundo_turno_sexta_saida or '21:00' }}">
                            </div>
                        </div>

                        <!-- Visualização dos horários -->
                        <div class="horario-preview">
                            <span class="horario-display" id="segundo-sexta-display">
                                <span id="segundo-sexta-entrada">14:00</span> - <span id="segundo-sexta-saida-jantar">18:00</span> /
                                <span id="segundo-sexta-entrada-jantar">19:00</span> - <span id="segundo-sexta-saida">21:00</span>
                            </span>
                        </div>
                    </div>

                    <!-- Configurações do Segundo Turno -->
                    <div class="horario-config" style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #d1d5db;">
                        <div class="config-grid">
                            <div class="config-item">
                                <div class="form-check">
                                    <input type="checkbox"
                                           id="segundo_turno_ativo"
                                           name="segundo_turno_ativo"
                                           value="1"
                                           {{ 'checked' if dados.segundo_turno_ativo else '' }}
                                           class="form-check-input">
                                    <label for="segundo_turno_ativo" class="form-check-label">
                                        <i class="fas fa-toggle-on text-success"></i>
                                        <strong>Segundo Turno:</strong> Ativo
                                    </label>
                                </div>
                            </div>
                            <div class="config-item">
                                <label class="form-label-small">
                                    <i class="fas fa-stopwatch text-info"></i>
                                    Tolerância 2º Turno
                                </label>
                                <div class="input-group">
                                    <input type="number"
                                           name="segundo_turno_tolerancia_minutos"
                                           class="form-control"
                                           value="{{ dados.segundo_turno_tolerancia_minutos or 15 }}"
                                           min="0" max="60"
                                           id="segundo-tolerancia-input">
                                    <div class="input-group-append">
                                        <span class="input-group-text">minutos</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    Segundo turno é opcional. Funcionários podem ser alocados especificamente para este horário.
                </small>
            </div>

            <!-- Status -->
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox"
                           id="ativa"
                           name="ativa"
                           value="1"
                           {{ 'checked' if dados.ativa else '' }}>
                    <label for="ativa">
                        <i class="fas fa-toggle-on"></i> Empresa ativa
                    </label>
                </div>
                <small class="text-muted">Empresas inativas não aparecerão nas opções de seleção</small>
            </div>

            <!-- Ações -->
            <div class="form-actions">
                <a href="{{ url_for('configuracoes.listar_empresas') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancelar
                </a>
                <button type="submit" class="btn btn-primary" id="btn-salvar">
                    <i class="fas fa-save"></i> Salvar Empresa
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading" id="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <div>Salvando empresa...</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-empresa');
    const cnpjInput = document.getElementById('cnpj');
    const telefoneInput = document.getElementById('telefone');
    const emailInput = document.getElementById('email');
    const btnSalvar = document.getElementById('btn-salvar');
    const loadingOverlay = document.getElementById('loading-overlay');
    
    // Máscaras
    cnpjInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length <= 14) {
            value = value.replace(/^(\d{2})(\d)/, '$1.$2');
            value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
            value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
            value = value.replace(/(\d{4})(\d)/, '$1-$2');
        }
        this.value = value;
        
        // Validar CNPJ em tempo real
        if (value.length === 18) {
            validarCNPJ(value);
        } else {
            limparFeedback('cnpj');
        }
    });
    
    telefoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length <= 11) {
            if (value.length <= 10) {
                value = value.replace(/^(\d{2})(\d)/, '($1) $2');
                value = value.replace(/(\d{4})(\d)/, '$1-$2');
            } else {
                value = value.replace(/^(\d{2})(\d)/, '($1) $2');
                value = value.replace(/(\d{5})(\d)/, '$1-$2');
            }
        }
        this.value = value;
    });
    
    // Validação de email
    emailInput.addEventListener('blur', function() {
        if (this.value) {
            validarEmail(this.value);
        } else {
            limparFeedback('email');
        }
    });
    
    // Submit do formulário
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validarFormulario()) {
            salvarEmpresa();
        }
    });

    // Atualização em tempo real dos horários
    function atualizarVisualizacaoHorarios() {
        // Segunda a Quinta
        const segEntrada = document.querySelector('input[name="jornada_segunda_entrada"]').value || '08:00';
        const segSaidaAlmoco = document.querySelector('input[name="jornada_segunda_saida_almoco"]').value || '12:00';
        const segEntradaAlmoco = document.querySelector('input[name="jornada_segunda_entrada_almoco"]').value || '13:00';
        const segSaida = document.querySelector('input[name="jornada_segunda_saida"]').value || '17:00';

        document.getElementById('seg-entrada').textContent = segEntrada;
        document.getElementById('seg-saida-almoco').textContent = segSaidaAlmoco;
        document.getElementById('seg-entrada-almoco').textContent = segEntradaAlmoco;
        document.getElementById('seg-saida').textContent = segSaida;

        // Sexta-feira
        const sextaEntrada = document.querySelector('input[name="jornada_sexta_entrada"]').value || '08:00';
        const sextaSaidaAlmoco = document.querySelector('input[name="jornada_sexta_saida_almoco"]').value || '12:00';
        const sextaEntradaAlmoco = document.querySelector('input[name="jornada_sexta_entrada_almoco"]').value || '13:00';
        const sextaSaida = document.querySelector('input[name="jornada_sexta_saida"]').value || '16:30';

        document.getElementById('sexta-entrada').textContent = sextaEntrada;
        document.getElementById('sexta-saida-almoco').textContent = sextaSaidaAlmoco;
        document.getElementById('sexta-entrada-almoco').textContent = sextaEntradaAlmoco;
        document.getElementById('sexta-saida').textContent = sextaSaida;

        // ========================================
        // SEGUNDO TURNO - ATUALIZAÇÃO EM TEMPO REAL
        // ========================================

        // Segunda a Quinta (Segundo Turno)
        const segundoSegEntrada = document.querySelector('input[name="segundo_turno_segunda_entrada"]')?.value || '14:00';
        const segundoSegSaidaJantar = document.querySelector('input[name="segundo_turno_segunda_saida_jantar"]')?.value || '18:00';
        const segundoSegEntradaJantar = document.querySelector('input[name="segundo_turno_segunda_entrada_jantar"]')?.value || '19:00';
        const segundoSegSaida = document.querySelector('input[name="segundo_turno_segunda_saida"]')?.value || '22:00';

        const segundoSegEntradaEl = document.getElementById('segundo-seg-entrada');
        const segundoSegSaidaJantarEl = document.getElementById('segundo-seg-saida-jantar');
        const segundoSegEntradaJantarEl = document.getElementById('segundo-seg-entrada-jantar');
        const segundoSegSaidaEl = document.getElementById('segundo-seg-saida');

        if (segundoSegEntradaEl) segundoSegEntradaEl.textContent = segundoSegEntrada;
        if (segundoSegSaidaJantarEl) segundoSegSaidaJantarEl.textContent = segundoSegSaidaJantar;
        if (segundoSegEntradaJantarEl) segundoSegEntradaJantarEl.textContent = segundoSegEntradaJantar;
        if (segundoSegSaidaEl) segundoSegSaidaEl.textContent = segundoSegSaida;

        // Sexta-feira (Segundo Turno)
        const segundoSextaEntrada = document.querySelector('input[name="segundo_turno_sexta_entrada"]')?.value || '14:00';
        const segundoSextaSaidaJantar = document.querySelector('input[name="segundo_turno_sexta_saida_jantar"]')?.value || '18:00';
        const segundoSextaEntradaJantar = document.querySelector('input[name="segundo_turno_sexta_entrada_jantar"]')?.value || '19:00';
        const segundoSextaSaida = document.querySelector('input[name="segundo_turno_sexta_saida"]')?.value || '21:00';

        const segundoSextaEntradaEl = document.getElementById('segundo-sexta-entrada');
        const segundoSextaSaidaJantarEl = document.getElementById('segundo-sexta-saida-jantar');
        const segundoSextaEntradaJantarEl = document.getElementById('segundo-sexta-entrada-jantar');
        const segundoSextaSaidaEl = document.getElementById('segundo-sexta-saida');

        if (segundoSextaEntradaEl) segundoSextaEntradaEl.textContent = segundoSextaEntrada;
        if (segundoSextaSaidaJantarEl) segundoSextaSaidaJantarEl.textContent = segundoSextaSaidaJantar;
        if (segundoSextaEntradaJantarEl) segundoSextaEntradaJantarEl.textContent = segundoSextaEntradaJantar;
        if (segundoSextaSaidaEl) segundoSextaSaidaEl.textContent = segundoSextaSaida;
    }

    // Adicionar listeners para todos os campos de horário (primeiro e segundo turno)
    const camposHorario = [
        // Primeiro turno
        'jornada_segunda_entrada', 'jornada_segunda_saida_almoco',
        'jornada_segunda_entrada_almoco', 'jornada_segunda_saida',
        'jornada_sexta_entrada', 'jornada_sexta_saida_almoco',
        'jornada_sexta_entrada_almoco', 'jornada_sexta_saida',

        // Segundo turno
        'segundo_turno_segunda_entrada', 'segundo_turno_segunda_saida_jantar',
        'segundo_turno_segunda_entrada_jantar', 'segundo_turno_segunda_saida',
        'segundo_turno_sexta_entrada', 'segundo_turno_sexta_saida_jantar',
        'segundo_turno_sexta_entrada_jantar', 'segundo_turno_sexta_saida'
    ];

    camposHorario.forEach(campo => {
        const input = document.querySelector(`input[name="${campo}"]`);
        if (input) {
            input.addEventListener('change', atualizarVisualizacaoHorarios);
            input.addEventListener('input', atualizarVisualizacaoHorarios);
        }
    });

    // Atualizar visualização inicial
    atualizarVisualizacaoHorarios();

    function validarCNPJ(cnpj) {
        const cnpjLimpo = cnpj.replace(/\D/g, '');
        
        if (cnpjLimpo.length !== 14) {
            mostrarFeedback('cnpj', 'CNPJ deve ter 14 dígitos', 'invalid');
            return false;
        }
        
        // Validação básica de CNPJ
        if (!validarCNPJDigitos(cnpjLimpo)) {
            mostrarFeedback('cnpj', 'CNPJ inválido', 'invalid');
            return false;
        }
        
        // Validar no servidor
        fetch('/configuracoes/api/validar-cnpj', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                cnpj: cnpj,
                empresa_id: {{ empresa_id if empresa_id else 'null' }}
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.valido) {
                mostrarFeedback('cnpj', data.message, 'valid');
                cnpjInput.value = data.cnpj_formatado;
            } else {
                mostrarFeedback('cnpj', data.message, 'invalid');
            }
        })
        .catch(error => {
            console.error('Erro na validação:', error);
            mostrarFeedback('cnpj', 'Erro na validação do CNPJ', 'invalid');
        });
        
        return true;
    }
    
    function validarCNPJDigitos(cnpj) {
        // Verifica se todos os dígitos são iguais
        if (/^(\d)\1{13}$/.test(cnpj)) return false;
        
        // Validação dos dígitos verificadores
        let soma = 0;
        let multiplicador = 5;
        
        for (let i = 0; i < 12; i++) {
            soma += parseInt(cnpj.charAt(i)) * multiplicador;
            multiplicador = multiplicador === 2 ? 9 : multiplicador - 1;
        }
        
        let resto = soma % 11;
        let dv1 = resto < 2 ? 0 : 11 - resto;
        
        if (parseInt(cnpj.charAt(12)) !== dv1) return false;
        
        soma = 0;
        multiplicador = 6;
        
        for (let i = 0; i < 13; i++) {
            soma += parseInt(cnpj.charAt(i)) * multiplicador;
            multiplicador = multiplicador === 2 ? 9 : multiplicador - 1;
        }
        
        resto = soma % 11;
        let dv2 = resto < 2 ? 0 : 11 - resto;
        
        return parseInt(cnpj.charAt(13)) === dv2;
    }
    
    function validarEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (emailRegex.test(email)) {
            mostrarFeedback('email', 'Email válido', 'valid');
            return true;
        } else {
            mostrarFeedback('email', 'Email inválido', 'invalid');
            return false;
        }
    }
    
    function mostrarFeedback(campo, mensagem, tipo) {
        const feedback = document.getElementById(campo + '-feedback');
        const input = document.getElementById(campo);

        // Verificar se os elementos existem antes de manipular
        if (!feedback) {
            console.error(`Elemento de feedback não encontrado: ${campo}-feedback`);
            return;
        }

        if (!input) {
            console.error(`Elemento de input não encontrado: ${campo}`);
            return;
        }

        feedback.textContent = mensagem;
        feedback.className = `validation-feedback ${tipo}`;
        feedback.style.display = 'block';

        input.classList.remove('is-valid', 'is-invalid');
        input.classList.add(tipo === 'valid' ? 'is-valid' : 'is-invalid');
    }
    
    function limparFeedback(campo) {
        const feedback = document.getElementById(campo + '-feedback');
        const input = document.getElementById(campo);

        // Verificar se os elementos existem antes de manipular
        if (feedback) {
            feedback.style.display = 'none';
        }

        if (input) {
            input.classList.remove('is-valid', 'is-invalid');
        }
    }
    
    function validarFormulario() {
        let valido = true;
        
        // Validar razão social
        const razaoSocial = document.getElementById('razao_social').value.trim();
        if (!razaoSocial) {
            mostrarFeedback('razao_social', 'Razão social é obrigatória', 'invalid');
            valido = false;
        } else if (razaoSocial.length < 3) {
            mostrarFeedback('razao_social', 'Razão social deve ter pelo menos 3 caracteres', 'invalid');
            valido = false;
        } else {
            limparFeedback('razao_social');
        }
        
        // Validar CNPJ
        const cnpj = cnpjInput.value.trim();
        if (!cnpj) {
            mostrarFeedback('cnpj', 'CNPJ é obrigatório', 'invalid');
            valido = false;
        } else if (cnpj.length !== 18) {
            mostrarFeedback('cnpj', 'CNPJ incompleto', 'invalid');
            valido = false;
        }
        
        // Validar email se preenchido
        const email = emailInput.value.trim();
        if (email && !validarEmail(email)) {
            valido = false;
        }
        
        return valido;
    }
    
    function salvarEmpresa() {
        loadingOverlay.style.display = 'flex';
        btnSalvar.disabled = true;
        
        // Submeter formulário
        form.submit();
    }
});
</script>
{% endblock %} 