<!-- Detalhes do Cliente - Conteúdo do Modal -->
<div class="row">
    <!-- Informações da Empresa -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-building"></i> Informações da Empresa</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Razão Social:</strong></td>
                        <td>{{ cliente.razao_social }}</td>
                    </tr>
                    {% if cliente.nome_fantasia %}
                    <tr>
                        <td><strong>Nome Fantasia:</strong></td>
                        <td>{{ cliente.nome_fantasia }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>CNPJ:</strong></td>
                        <td>{{ cliente.cnpj }}</td>
                    </tr>
                    {% if cliente.telefone %}
                    <tr>
                        <td><strong>Telefone:</strong></td>
                        <td>{{ cliente.telefone }}</td>
                    </tr>
                    {% endif %}
                    {% if cliente.email %}
                    <tr>
                        <td><strong>E-mail:</strong></td>
                        <td>{{ cliente.email }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
    
    <!-- Informações do Contrato -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-file-contract"></i> Informações do Contrato</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    {% if cliente.nome_contrato %}
                    <tr>
                        <td><strong>Nome do Contrato:</strong></td>
                        <td>{{ cliente.nome_contrato }}</td>
                    </tr>
                    {% endif %}
                    {% if cliente.codigo_contrato %}
                    <tr>
                        <td><strong>Código:</strong></td>
                        <td>{{ cliente.codigo_contrato }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge badge-{{ 'success' if cliente.status_contrato == 'ativo' else 'warning' if cliente.status_contrato == 'pausado' else 'danger' }}">
                                {{ cliente.status_contrato.title() }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Data de Início:</strong></td>
                        <td>{{ cliente.data_inicio.strftime('%d/%m/%Y') if cliente.data_inicio else 'N/A' }}</td>
                    </tr>
                    {% if cliente.data_fim %}
                    <tr>
                        <td><strong>Data de Fim:</strong></td>
                        <td>{{ cliente.data_fim.strftime('%d/%m/%Y') }}</td>
                    </tr>
                    {% endif %}
                    {% if cliente.valor_contrato %}
                    <tr>
                        <td><strong>Valor do Contrato:</strong></td>
                        <td>R$ {{ "%.2f"|format(cliente.valor_contrato) }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>Criado em:</strong></td>
                        <td>{{ cliente.created_at.strftime('%d/%m/%Y %H:%M') if cliente.created_at else 'N/A' }}</td>
                    </tr>
                    {% if cliente.updated_at and cliente.updated_at != cliente.created_at %}
                    <tr>
                        <td><strong>Atualizado em:</strong></td>
                        <td>{{ cliente.updated_at.strftime('%d/%m/%Y %H:%M') }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Descrição do Projeto -->
{% if cliente.descricao_projeto %}
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-project-diagram"></i> Descrição do Projeto</h6>
            </div>
            <div class="card-body">
                <p>{{ cliente.descricao_projeto }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Observações -->
{% if cliente.observacoes %}
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-sticky-note"></i> Observações</h6>
            </div>
            <div class="card-body">
                <p>{{ cliente.observacoes }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Funcionários da Empresa -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-users"></i> Funcionários da Empresa ({{ funcionarios|length }})</h6>
            </div>
            <div class="card-body">
                {% if funcionarios %}
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Cargo</th>
                                    <th>Tipo de Vínculo</th>
                                    <th>Jornada</th>
                                    <th>Telefone</th>
                                    <th>Detalhes da Alocação</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for func in funcionarios %}
                                <tr>
                                    <td>
                                        <strong>{{ func.nome }}</strong><br>
                                        <small class="text-muted">{{ func.cpf }}</small>
                                    </td>
                                    <td>{{ func.cargo }}</td>
                                    <td>
                                        {% if func.tipo_vinculo == 'Alocado' %}
                                            <span class="badge badge-primary">{{ func.tipo_vinculo }}</span>
                                        {% else %}
                                            <span class="badge badge-success">{{ func.tipo_vinculo }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if func.jornada_nome %}
                                            {{ func.jornada_nome }}<br>
                                            <small class="text-muted">{{ func.carga_horaria }}h</small>
                                        {% else %}
                                            <span class="text-muted">Não definida</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ func.telefone or '-' }}</td>
                                    <td>
                                        {% if func.tipo_vinculo == 'Alocado' %}
                                            {% if func.percentual_alocacao %}
                                                <strong>{{ func.percentual_alocacao }}%</strong><br>
                                            {% endif %}
                                            {% if func.data_inicio %}
                                                <small>Desde: {{ func.data_inicio.strftime('%d/%m/%Y') }}</small><br>
                                            {% endif %}
                                            {% if func.data_fim %}
                                                <small>Até: {{ func.data_fim.strftime('%d/%m/%Y') }}</small>
                                            {% else %}
                                                <small class="text-success">Indefinido</small>
                                            {% endif %}
                                            {% if func.valor_hora %}
                                                <br><small class="text-muted">R$ {{ "%.2f"|format(func.valor_hora) }}/h</small>
                                            {% endif %}
                                        {% else %}
                                            <small class="text-muted">Funcionário direto da empresa</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="/funcionarios/{{ func.funcionario_id }}" class="btn btn-sm btn-info" target="_blank">
                                            <i class="fas fa-eye"></i> Ver
                                        </a>
                                        {% if func.tipo_vinculo == 'Alocado' and func.alocacao_ativa %}
                                            <button class="btn btn-sm btn-warning" onclick="editarAlocacao({{ func.funcionario_id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-user-slash fa-2x text-muted mb-2"></i>
                        <p class="text-muted">Nenhum funcionário alocado para este cliente</p>
                        <button class="btn btn-primary btn-sm" onclick="$('#modalDetalhesCliente').modal('hide'); alocarFuncionario({{ cliente.empresa_cliente_id }});">
                            <i class="fas fa-user-plus"></i> Alocar Funcionário
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function desalocarFuncionario(alocacaoId) {
    if (confirm('Deseja desalocar este funcionário?')) {
        // Implementar desalocação
        alert('Desalocar funcionário ' + alocacaoId + ' - será implementado');
    }
}

function realocarFuncionario(alocacaoId) {
    if (confirm('Deseja realocar este funcionário?')) {
        // Implementar realocação
        alert('Realocar funcionário ' + alocacaoId + ' - será implementado');
    }
}
</script>
