#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_jornadas_api():
    """Testar API de jornadas para verificar campos retornados"""
    
    print("=== TESTE API JORNADAS ===\n")
    
    # Fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    login_response = session.post('http://************/login', data=login_data)
    
    print(f"1. Login status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ Falha no login")
        return
    
    # Testar API de jornadas da empresa AiNexus (ID 11)
    print(f"\n2. Testando API de jornadas da empresa AiNexus (ID 11):")
    api_response = session.get('http://************/empresa-principal/api/jornadas-empresa/11')
    
    if api_response.status_code == 200:
        data = api_response.json()
        print(f"   ✅ Status: {api_response.status_code}")
        print(f"   Success: {data.get('success')}")
        print(f"   Total jornadas: {len(data.get('jornadas', []))}")
        
        jornadas = data.get('jornadas', [])
        for i, jornada in enumerate(jornadas):
            print(f"\n   📋 Jornada {i+1}:")
            print(f"     ID: {jornada.get('id')}")
            print(f"     nome: '{jornada.get('nome')}'")
            print(f"     nome_jornada: '{jornada.get('nome_jornada')}'")
            print(f"     tipo_jornada: '{jornada.get('tipo_jornada')}'")
            print(f"     carga_horaria: '{jornada.get('carga_horaria')}'")
            print(f"     empresa_nome: '{jornada.get('empresa_nome')}'")
            
            # Verificar qual campo tem o nome da jornada
            if jornada.get('nome'):
                print(f"     ✅ Campo 'nome' disponível: {jornada.get('nome')}")
            if jornada.get('nome_jornada'):
                print(f"     ✅ Campo 'nome_jornada' disponível: {jornada.get('nome_jornada')}")
    else:
        print(f"   ❌ Erro: {api_response.status_code}")
        print(f"   Resposta: {api_response.text[:200]}")
    
    # Testar também a API de alocação para ver os campos retornados
    print(f"\n3. Testando API de alocação (funcionário TESTE MOV 2 - ID 72):")
    alocacao_response = session.get('http://************/empresa-principal/api/alocacao-funcionario/72')
    
    if alocacao_response.status_code == 200:
        alocacao_data = alocacao_response.json()
        if alocacao_data.get('success'):
            alocacao = alocacao_data.get('alocacao')
            print(f"   ✅ Alocação encontrada:")
            print(f"     jornada_trabalho_id: {alocacao.get('jornada_trabalho_id')}")
            print(f"     nome_jornada: '{alocacao.get('nome_jornada')}'")
            print(f"     tipo_jornada: '{alocacao.get('tipo_jornada')}'")
        else:
            print(f"   ❌ Erro: {alocacao_data.get('message')}")
    else:
        print(f"   ❌ Erro: {alocacao_response.status_code}")

if __name__ == "__main__":
    test_jornadas_api()
