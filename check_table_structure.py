#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== ESTRUTURA COMPLETA DA TABELA funcionario_alocacoes ===\n")
        
        estrutura = db.execute_query("DESCRIBE funcionario_alocacoes")
        
        for campo in estrutura:
            print(f"Campo: {campo['Field']}")
            print(f"  Tipo: {campo['Type']}")
            print(f"  Null: {campo['Null']}")
            print(f"  Default: {campo['Default']}")
            print(f"  Extra: {campo.get('Extra', 'N/A')}")
            print()
        
        # Verificar se existe o campo dados_jornada_nova
        campos_problematicos = []
        for campo in estrutura:
            if campo['Null'] == 'NO' and campo['Default'] is None and campo.get('Extra') != 'auto_increment':
                campos_problematicos.append(campo['Field'])
        
        print("=== CAMPOS OBRIGATÓRIOS SEM DEFAULT ===")
        for campo in campos_problematicos:
            print(f"❌ {campo}")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
