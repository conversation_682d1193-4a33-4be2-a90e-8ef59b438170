# 📌 Rotina Inicial da IA

## 🧠 Objetivo desta Rotina
Esta rotina serve como guia obrigatório para cada nova sessão ou conversa iniciada pela IA no ambiente do projeto. O objetivo é garantir foco, alinhamento com a lógica do sistema, e prevenção de delírios ou desvios criativos durante o processo de criação.

---

## ✅ Etapas da Rotina de Inicialização

### 1. **Checagem Geral do Sistema**
- Ler os arquivos de introdução ao sistema e estrutura de funcionamento.
- Entender a **filosofia**, **forma de trabalho**, **propósito** e **lógica** do sistema.
- Reforçar os pilares do processo de produção.

### 2. **Leitura dos Documentos Base**
- Localizar e ler os documentos principais que estão armazenados na pasta:  
  `./docs/`
- Estes documentos contêm as **regras, diretrizes e processos fundamentais** da produção.
- Leitura obrigatória de todos os documentos contidos, com excessão do log_continuação.md, é obrigatorio ler apenas o ultimo procedimento feito, não nessario a leitura completa..

### 3. **Verificação dos Servidores MCP**
- **OBRIGATÓRIO**: Verificar se os servidores MCP estão configurados e funcionando antes de iniciar qualquer atividade.
- **Se MCP NÃO estiver instalado:**
  - Consultar `docs/mcp-instalacao-concluida.md` para instalação completa
  - Seguir todos os passos de instalação e configuração
- **Se MCP JÁ estiver instalado:**
  - Executar verificação usando `docs/mcp-check.md`
  - Confirmar que todos os 4 servidores estão funcionando:
    - GitHub MCP (Smithery CLI)
    - Browser Tools MCP
    - Context7 MCP
    - 21st-dev Magic MCP
- **Se houver problemas no MCP:**
  - Diagnosticar usando o checklist em `docs/mcp-check.md`
  - Corrigir problemas identificados antes de prosseguir
  - Reinstalar pacotes se necessário
- **Teste rápido obrigatório:**
  ```powershell
  # Verificar Node.js
  node --version

  # Testar GitHub MCP
  npx @smithery/cli@latest --help

  # Verificar arquivo de configuração
  Test-Path ".vscode\settings.json"
  ```

### 4. **Compromisso com o Processo**
- Toda resposta gerada deve estar **alinhada com os guias e documentos do sistema**.
- **Evitar delírios criativos** que não estejam de acordo com os documentos.
- Em caso de dúvida sobre como proceder:
  - **Revisitar imediatamente os documentos da pasta `docs/`** antes de continuar a criação.

---

## 🚨 Importante
> Nenhuma decisão deve ser tomada sem consultar os guias principais do projeto.
> A IA deve assumir uma postura de **criação guiada, consciente e focada**.
> **SEMPRE verificar se os servidores MCP estão funcionando** antes de iniciar atividades.

---

## 🔥 REGRA CRÍTICA - ERRO 502 BAD GATEWAY

### ⚠️ **ATENÇÃO MÁXIMA - ERRO GROTESCO INACEITÁVEL**

**JAMAIS** qualquer IA trabalhando neste projeto pode deixar o sistema apresentar **502 Bad Gateway**. Este é um erro **GROTESCO** e **INACEITÁVEL** que quebra completamente o sistema.

### 🚨 **PROTOCOLO OBRIGATÓRIO DE TESTE**

**ANTES DE ENTREGAR QUALQUER SOLUÇÃO**, a IA **DEVE OBRIGATORIAMENTE**:

1. ✅ **TESTAR O SERVIÇO FLASK**
   ```bash
   ssh rlponto-server "ps aux | grep python | grep app.py"
   ```

2. ✅ **VERIFICAR SE ESTÁ RESPONDENDO**
   ```bash
   ssh rlponto-server "curl -s -o /dev/null -w '%{http_code}' http://localhost:5000/"
   ```

3. ✅ **CONFIRMAR NO NAVEGADOR**
   - Abrir: `http://************:5000/`
   - Verificar se NÃO aparece "502 Bad Gateway"

4. ✅ **TESTAR A FUNCIONALIDADE ESPECÍFICA**
   - Acessar a página/funcionalidade modificada
   - Confirmar que está funcionando corretamente

### 🛡️ **CAUSAS COMUNS DO 502 BAD GATEWAY**

- **Serviço Flask parado** (mais comum)
- **Erro de sintaxe** no código Python
- **Porta 5000 não respondendo**
- **Processo Python travado**

### 🔧 **CORREÇÃO IMEDIATA**

Se detectar 502 Bad Gateway:

1. **Verificar logs de erro:**
   ```bash
   ssh rlponto-server "cd /var/www/controle-ponto && tail -20 app.log"
   ```

2. **Reiniciar o serviço:**
   ```bash
   ssh rlponto-server "pkill -f app.py && cd /var/www/controle-ponto && nohup python3 app.py > app.log 2>&1 &"
   ```

3. **Testar novamente** até confirmar funcionamento

### 🚫 **PROIBIÇÕES ABSOLUTAS**

- ❌ **NUNCA** entregar solução sem testar
- ❌ **NUNCA** assumir que "deve estar funcionando"
- ❌ **NUNCA** ignorar verificação de status do serviço
- ❌ **NUNCA** deixar o usuário descobrir o erro 502
- ❌ **NUNCA** Esquecer de fazer o deploy.
- ❌ **NUNCA** Antes de fazer alterações, faça backup do arquivo original.


### ✅ **COMPROMISSO OBRIGATÓRIO**

**TODA IA** que trabalhar neste projeto **JURA SOLENEMENTE**:

> "Eu, IA assistente, me comprometo a JAMAIS entregar uma solução sem antes testar completamente o sistema e garantir que NÃO há erro 502 Bad Gateway. Este erro é GROTESCO e INACEITÁVEL, e eu assumo total responsabilidade por preveni-lo."

### 🎯 **REGRA DE OURO**

**"SE NÃO TESTOU, NÃO ENTREGOU!"**

---

## 🔧 REGRA CRÍTICA - VERIFICAÇÃO MCP

### ⚠️ **PROTOCOLO OBRIGATÓRIO MCP**

**ANTES DE INICIAR QUALQUER ATIVIDADE**, a IA **DEVE OBRIGATORIAMENTE**:

1. ✅ **VERIFICAR SE MCP ESTÁ INSTALADO**
   - Verificar se existe `.vscode/settings.json`
   - Se NÃO existir: seguir `docs/mcp-instalacao-concluida.md`

2. ✅ **EXECUTAR VERIFICAÇÃO MCP**
   - Usar checklist em `docs/mcp-check.md`
   - Testar todos os 4 servidores MCP
   - Confirmar Node.js funcionando

3. ✅ **CORRIGIR PROBLEMAS IDENTIFICADOS**
   - Resolver problemas antes de prosseguir
   - Reinstalar pacotes se necessário
   - Verificar conectividade npm

### 🚨 **CAUSAS COMUNS DE PROBLEMAS MCP**

- **Node.js não encontrado** (após instalação)
- **Política de execução** bloqueada
- **Pacotes MCP corrompidos** ou desatualizados
- **Arquivo de configuração** ausente ou inválido
- **Conectividade npm** com problemas

### 🔧 **CORREÇÃO IMEDIATA MCP**

Se detectar problemas MCP:

1. **Verificar Node.js:**
   ```powershell
   node --version
   npm --version
   ```

2. **Testar servidores:**
   ```powershell
   npx @smithery/cli@latest --help
   npx @upstash/context7-mcp --help
   ```

3. **Reinstalar se necessário:**
   ```powershell
   npm cache clean --force
   npm install -g @smithery/cli @agentdeskai/browser-tools-mcp @upstash/context7-mcp @21st-dev/magic
   ```

### ✅ **COMPROMISSO MCP OBRIGATÓRIO**

**TODA IA** que trabalhar neste projeto **COMPROMETE-SE**:

> "Eu, IA assistente, me comprometo a SEMPRE verificar o status dos servidores MCP antes de iniciar qualquer atividade. Caso identifique problemas, irei corrigi-los imediatamente usando os documentos de referência apropriados, E NUNCA CRIAR DADOS DE SIMULAÇÕES SEM PERMISSÃO, ANTES DE QUALQUER ALTERAÇÃO CHECAR SE VAI AFETAR OUTRAS AREAS DO SISTEMA, SE AFETAR ARRUME UMA SOLUÇÃO QUE NÃO AFETE!.

ABORDAGEM CORRETA:
NÃO MUDAR a estrutura do projeto
NÃO CRIAR versões "melhoradas"
RESOLVER os problemas existentes
CORRIGIR as configurações que estão quebradas"

### 🎯 **REGRA DE OURO MCP**

**"SEM MCP FUNCIONANDO, SEM DESENVOLVIMENTO!"**

---