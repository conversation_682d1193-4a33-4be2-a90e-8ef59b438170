#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== DEBUG ERRO DE ALOCAÇÃO ===\n")
        
        # 1. Verificar se a tabela funcionario_alocacoes existe
        print("1. 🔍 VERIFICANDO TABELA funcionario_alocacoes:")
        try:
            estrutura = db.execute_query("DESCRIBE funcionario_alocacoes")
            print("   ✅ Tabela existe!")
            print("   📋 Estrutura:")
            for campo in estrutura:
                print(f"      - {campo['Field']}: {campo['Type']} | Null: {campo['Null']} | Default: {campo['Default']}")
        except Exception as e:
            print(f"   ❌ Erro ao acessar tabela: {e}")
            return
        
        # 2. Verificar dados de teste para alocação
        print(f"\n2. 🧪 DADOS DE TESTE:")
        
        # Buscar um funcionário ativo
        funcionarios = db.execute_query("""
            SELECT id, nome_completo, empresa_id 
            FROM funcionarios 
            WHERE status_cadastro = 'Ativo' 
            LIMIT 1
        """)
        
        if funcionarios:
            funcionario = funcionarios[0]
            print(f"   👤 Funcionário teste: ID {funcionario['id']} - {funcionario['nome_completo']}")
        else:
            print("   ❌ Nenhum funcionário ativo encontrado")
            return
        
        # Buscar empresa cliente (AiNexus)
        clientes = db.execute_query("""
            SELECT empresa_cliente_id 
            FROM empresa_clientes 
            WHERE empresa_cliente_id = 11
        """)
        
        if clientes:
            cliente_id = clientes[0]['empresa_cliente_id']
            print(f"   🏢 Cliente teste: ID {cliente_id} (AiNexus)")
        else:
            print("   ❌ Cliente AiNexus não encontrado")
            return
        
        # Buscar jornada ativa
        jornadas = db.execute_query("""
            SELECT id, nome_jornada 
            FROM jornadas_trabalho 
            WHERE empresa_id = 11 AND ativa = TRUE 
            LIMIT 1
        """)
        
        if jornadas:
            jornada = jornadas[0]
            print(f"   ⏰ Jornada teste: ID {jornada['id']} - {jornada['nome_jornada']}")
        else:
            print("   ❌ Nenhuma jornada ativa encontrada")
            return
        
        # 3. Testar inserção manual
        print(f"\n3. 🧪 TESTE DE INSERÇÃO:")
        
        dados_teste = {
            'funcionario_id': funcionario['id'],
            'empresa_cliente_id': cliente_id,
            'jornada_trabalho_id': jornada['id'],
            'cargo_no_cliente': 'TESTE CARGO',
            'data_inicio': '2025-07-21',
            'data_fim': None,
            'percentual_alocacao': 100,
            'valor_hora': None,
            'observacoes': 'Teste de alocação'
        }
        
        print(f"   📝 Dados para inserção:")
        for key, value in dados_teste.items():
            print(f"      {key}: {value}")
        
        # Verificar se já existe alocação
        sql_check = """
        SELECT id FROM funcionario_alocacoes
        WHERE funcionario_id = %s AND empresa_cliente_id = %s
        AND ativo = 1 AND (data_fim IS NULL OR data_fim >= CURDATE())
        """
        
        existing = db.execute_query(sql_check, (dados_teste['funcionario_id'], dados_teste['empresa_cliente_id']))
        if existing:
            print(f"   ⚠️ Alocação já existe: ID {existing[0]['id']}")
            
            # Remover alocação existente para teste
            db.execute_query("DELETE FROM funcionario_alocacoes WHERE id = %s", (existing[0]['id'],))
            print(f"   🗑️ Alocação existente removida para teste")
        
        # Tentar inserção
        sql_insert = """
        INSERT INTO funcionario_alocacoes
        (funcionario_id, empresa_cliente_id, jornada_trabalho_id, cargo_no_cliente,
         data_inicio, data_fim, percentual_alocacao, valor_hora, observacoes, ativo)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, 1)
        """
        
        params = (
            dados_teste['funcionario_id'], dados_teste['empresa_cliente_id'], dados_teste['jornada_trabalho_id'],
            dados_teste['cargo_no_cliente'], dados_teste['data_inicio'], dados_teste['data_fim'],
            dados_teste['percentual_alocacao'], dados_teste['valor_hora'], dados_teste['observacoes']
        )
        
        try:
            result = db.execute_query(sql_insert, params, fetch_all=False)
            print(f"   ✅ Inserção bem-sucedida! Resultado: {result}")
            
            # Verificar se foi inserido
            verificacao = db.execute_query("""
                SELECT id, funcionario_id, empresa_cliente_id, cargo_no_cliente, data_inicio
                FROM funcionario_alocacoes 
                WHERE funcionario_id = %s AND empresa_cliente_id = %s
                ORDER BY id DESC LIMIT 1
            """, (dados_teste['funcionario_id'], dados_teste['empresa_cliente_id']))
            
            if verificacao:
                print(f"   ✅ Alocação criada: ID {verificacao[0]['id']}")
                
                # Limpar teste
                db.execute_query("DELETE FROM funcionario_alocacoes WHERE id = %s", (verificacao[0]['id'],))
                print(f"   🧹 Teste limpo")
            
        except Exception as e:
            print(f"   ❌ Erro na inserção: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Verificar campos obrigatórios
        print(f"\n4. 🔍 VERIFICAÇÃO DE CAMPOS OBRIGATÓRIOS:")
        campos_obrigatorios = ['funcionario_id', 'empresa_cliente_id', 'jornada_trabalho_id', 'data_inicio']
        
        for campo in campos_obrigatorios:
            if dados_teste[campo] is None or dados_teste[campo] == '':
                print(f"   ❌ Campo obrigatório vazio: {campo}")
            else:
                print(f"   ✅ Campo OK: {campo} = {dados_teste[campo]}")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
