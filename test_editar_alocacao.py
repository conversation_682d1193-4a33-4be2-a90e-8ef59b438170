#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_editar_alocacao():
    """Testar edição de alocação para identificar problema de validação"""
    
    print("=== TESTE EDITAR ALOCAÇÃO ===\n")
    
    # Fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    login_response = session.post('http://************/login', data=login_data)
    
    print(f"1. Login status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ Falha no login")
        return
    
    # Primeiro, buscar dados da alocação atual
    print(f"\n2. Buscando dados da alocação atual (funcionário ID 72):")
    alocacao_response = session.get('http://************/empresa-principal/api/alocacao-funcionario/72')
    
    if alocacao_response.status_code != 200:
        print(f"❌ Erro ao buscar alocação: {alocacao_response.status_code}")
        return
    
    alocacao_data = alocacao_response.json()
    if not alocacao_data.get('success'):
        print(f"❌ Erro: {alocacao_data.get('message')}")
        return
    
    alocacao = alocacao_data.get('alocacao')
    print(f"   ✅ Alocação encontrada:")
    print(f"     ID: {alocacao['id']}")
    print(f"     Funcionário ID: {alocacao['funcionario_id']}")
    print(f"     Cliente ID: {alocacao['empresa_cliente_id']}")
    print(f"     Jornada ID: {alocacao['jornada_trabalho_id']}")
    print(f"     Cargo: {alocacao['cargo_no_cliente']}")
    print(f"     Data início: {alocacao['data_inicio']}")
    
    # Simular envio do formulário de edição
    print(f"\n3. Testando edição da alocação:")
    
    # Dados do formulário (simulando o que seria enviado)
    form_data = {
        'alocacao_id': str(alocacao['id']),
        'funcionario_id': str(alocacao['funcionario_id']),
        'empresa_cliente_id': str(alocacao['empresa_cliente_id']),
        'jornada_trabalho_id': str(alocacao['jornada_trabalho_id']),
        'cargo_no_cliente': alocacao['cargo_no_cliente'],
        'data_inicio': alocacao['data_inicio'],
        'data_fim': alocacao['data_fim'] or '',
        'percentual_alocacao': str(alocacao.get('percentual_alocacao', 100)),
        'valor_hora': str(alocacao.get('valor_hora', '') or ''),
        'observacoes': alocacao.get('observacoes', '') or ''
    }
    
    print(f"   📋 Dados que serão enviados:")
    for key, value in form_data.items():
        print(f"     {key}: '{value}' (tipo: {type(value)})")
    
    # Enviar requisição de edição
    edit_response = session.post('http://************/empresa-principal/alocacoes/editar', data=form_data)
    
    print(f"\n4. Resultado da edição:")
    print(f"   Status: {edit_response.status_code}")
    
    if edit_response.status_code == 200:
        edit_data = edit_response.json()
        print(f"   Success: {edit_data.get('success')}")
        print(f"   Message: {edit_data.get('message')}")
        
        if not edit_data.get('success'):
            print(f"   ❌ Erro específico: {edit_data.get('message')}")
        else:
            print(f"   ✅ Edição realizada com sucesso!")
    else:
        print(f"   ❌ Erro HTTP: {edit_response.status_code}")
        print(f"   Resposta: {edit_response.text[:200]}")
    
    # Testar com campo vazio para confirmar validação
    print(f"\n5. Testando validação (cargo vazio):")
    form_data_invalido = form_data.copy()
    form_data_invalido['cargo_no_cliente'] = ''
    
    invalid_response = session.post('http://************/empresa-principal/alocacoes/editar', data=form_data_invalido)
    
    if invalid_response.status_code == 200:
        invalid_data = invalid_response.json()
        print(f"   Success: {invalid_data.get('success')}")
        print(f"   Message: {invalid_data.get('message')}")
        
        if not invalid_data.get('success'):
            print(f"   ✅ Validação funcionando: {invalid_data.get('message')}")

if __name__ == "__main__":
    test_editar_alocacao()
