#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Utilitários de Autenticação - Controle de Ponto
------------------------------------------------

Este módulo centraliza funções relacionadas à autenticação,
autorização e segurança do sistema.
"""

import logging
from functools import wraps
from flask import session, request, redirect, url_for, jsonify
from werkzeug.security import check_password_hash

logger = logging.getLogger('controle-ponto.auth')

def is_password_hash(password):
    """
    Verifica se uma senha está em formato hash.
    
    Args:
        password (str): Senha a ser verificada
        
    Returns:
        bool: True se é um hash, False caso contrário
    """
    return ":" in password and len(password) > 50

def require_login(f):
    """
    Decorator para exigir autenticação.
    
    Args:
        f (function): Função a ser decorada
        
    Returns:
        function: Função decorada
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'usuario' not in session:
            if request.is_json:
                return jsonify({'error': 'Autenticação necessária'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def require_admin(f):
    """
    Decorator para exigir nível de acesso admin.
    
    Args:
        f (function): Função a ser decorada
        
    Returns:
        function: Função decorada
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Detectar requisições AJAX/API
        is_ajax = (request.is_json or
                  request.headers.get('X-Requested-With') == 'XMLHttpRequest' or
                  request.headers.get('Accept', '').find('application/json') != -1 or
                  request.path.startswith('/api/') or
                  '/api/' in request.path)

        if 'usuario' not in session:
            if is_ajax:
                return jsonify({'error': 'Autenticação necessária'}), 401
            return redirect(url_for('login'))

        if session.get('nivel_acesso') != 'admin':
            if is_ajax:
                return jsonify({'error': 'Acesso negado. Privilégios de administrador necessários'}), 403
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function

def check_password_change_required(f):
    """
    Decorator para verificar se troca de senha é obrigatória.
    
    Args:
        f (function): Função a ser decorada
        
    Returns:
        function: Função decorada
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if session.get('force_password_change', False):
            return redirect(url_for('trocar_senha_obrigatoria'))
        return f(*args, **kwargs)
    return decorated_function

def get_current_user():
    """
    Obtém informações do usuário atual da sessão.
    
    Returns:
        dict: Dados do usuário atual
    """
    return {
        'usuario': session.get('usuario'),
        'nivel_acesso': session.get('nivel_acesso', 'usuario'),
        'is_admin': session.get('nivel_acesso') == 'admin',
        'force_password_change': session.get('force_password_change', False)
    }

def validate_user_permissions(required_level='usuario'):
    """
    Valida se o usuário atual tem as permissões necessárias.
    
    Args:
        required_level (str): Nível de acesso necessário
        
    Returns:
        bool: True se tem permissão, False caso contrário
    """
    if 'usuario' not in session:
        return False
    
    user_level = session.get('nivel_acesso', 'usuario')
    
    # Hierarquia de permissões
    levels = {
        'usuario': 1,
        'admin': 2
    }
    
    user_level_value = levels.get(user_level, 0)
    required_level_value = levels.get(required_level, 0)
    
    return user_level_value >= required_level_value

class AuthManager:
    """
    Classe para gerenciar operações de autenticação mais complexas.
    """
    
    @staticmethod
    def login_user(usuario_data, nivel_acesso='usuario', force_password_change=False):
        """
        Realiza o login do usuário na sessão.
        
        Args:
            usuario_data (dict): Dados do usuário
            nivel_acesso (str): Nível de acesso do usuário
            force_password_change (bool): Se deve forçar troca de senha
        """
        session['usuario'] = usuario_data['usuario']
        session['nivel_acesso'] = nivel_acesso
        session['force_password_change'] = force_password_change
        session.permanent = True
        
        logger.info(f"Usuário {usuario_data['usuario']} logado com nível {nivel_acesso}")
    
    @staticmethod
    def logout_user():
        """
        Realiza logout do usuário atual.
        """
        usuario = session.get('usuario', 'Desconhecido')
        session.clear()
        logger.info(f"Usuário {usuario} deslogado")
    
    @staticmethod
    def verify_password(stored_password, provided_password):
        """
        Verifica se a senha fornecida confere com a armazenada.
        
        Args:
            stored_password (str): Senha armazenada no banco
            provided_password (str): Senha fornecida pelo usuário
            
        Returns:
            tuple: (is_valid, needs_update)
        """
        if is_password_hash(stored_password):
            # Senha é um hash, usar verificação segura
            is_valid = check_password_hash(stored_password, provided_password)
            needs_update = False
        else:
            # Senha em texto plano, precisa ser atualizada
            is_valid = stored_password == provided_password
            needs_update = True
            
        return is_valid, needs_update

# Constantes de autorização
ADMIN_DEFAULT_USERNAME = "admin"

# Funções auxiliares para templates
def get_template_context():
    """
    Obtém contexto comum para todos os templates.
    
    Returns:
        dict: Contexto para templates
    """
    user = get_current_user()
    return {
        'current_user': user,
        'is_admin': user['is_admin'],
        'show_debug': user['nivel_acesso'] == 'admin'
    }

# ========================================
# ALIASES PARA COMPATIBILIDADE
# ========================================

# Aliases para manter compatibilidade com código existente
login_required = require_login
admin_required = require_admin 