#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== INVESTIGAÇÃO COMPLETA EMPRESA AINEXUS ===\n")
        
        # 1. Buscar empresa AiNexus
        print("1. 🏢 DADOS DA EMPRESA AINEXUS:")
        empresas = db.execute_query("""
            SELECT id, razao_social, nome_fantasia, cnpj, empresa_principal,
                   ativa, data_cadastro
            FROM empresas
            WHERE razao_social LIKE %s
        """, ('%AiNexus%',))
        
        if not empresas:
            print("❌ Empresa AiNexus não encontrada!")
            return
            
        ainexus = empresas[0]
        print(f"   ID: {ainexus['id']}")
        print(f"   Razão Social: {ainexus['razao_social']}")
        print(f"   Nome Fantasia: {ainexus['nome_fantasia']}")
        print(f"   CNPJ: {ainexus['cnpj']}")
        print(f"   Empresa Principal: {ainexus['empresa_principal']}")
        print(f"   Ativa: {ainexus['ativa']}")
        print(f"   Data Cadastro: {ainexus['data_cadastro']}")
        
        # 2. Verificar jornadas da empresa
        print(f"\n2. ⏰ JORNADAS DA EMPRESA AINEXUS (ID: {ainexus['id']}):")
        jornadas = db.execute_query("""
            SELECT id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
                   seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
                   intervalo_inicio, intervalo_fim, ativa, padrao, empresa_id
            FROM jornadas_trabalho 
            WHERE empresa_id = %s
            ORDER BY padrao DESC, nome_jornada
        """, (ainexus['id'],))
        
        if jornadas:
            for j in jornadas:
                print(f"   📋 ID: {j['id']} | Nome: {j['nome_jornada']}")
                print(f"      Tipo: {j['tipo_jornada']} | Categoria: {j['categoria_funcionario']}")
                print(f"      Seg-Qui: {j['seg_qui_entrada']} às {j['seg_qui_saida']}")
                print(f"      Sexta: {j['sexta_entrada']} às {j['sexta_saida']}")
                print(f"      Intervalo: {j['intervalo_inicio']} às {j['intervalo_fim']}")
                print(f"      Ativa: {j['ativa']} | Padrão: {j['padrao']}")
                print(f"      Empresa ID: {j['empresa_id']}")
                print()
        else:
            print("   ❌ Nenhuma jornada encontrada!")
        
        # 3. Verificar se AiNexus é cliente de alguma empresa principal
        print("3. 🤝 RELACIONAMENTO COMO CLIENTE:")
        clientes = db.execute_query("""
            SELECT ec.id, ec.empresa_principal_id, ec.empresa_cliente_id, ec.ativa,
                   ep.razao_social as empresa_principal_nome,
                   ecl.razao_social as empresa_cliente_nome
            FROM empresa_clientes ec
            JOIN empresas ep ON ec.empresa_principal_id = ep.id
            JOIN empresas ecl ON ec.empresa_cliente_id = ecl.id
            WHERE ec.empresa_cliente_id = %s
        """, (ainexus['id'],))
        
        if clientes:
            for c in clientes:
                print(f"   🏢 Cliente de: {c['empresa_principal_nome']} (ID: {c['empresa_principal_id']})")
                print(f"   Status: {'Ativo' if c['ativa'] else 'Inativo'}")
        else:
            print("   ❌ AiNexus não é cliente de nenhuma empresa principal!")
        
        # 4. Verificar funcionários alocados para AiNexus
        print("\n4. 👥 FUNCIONÁRIOS ALOCADOS PARA AINEXUS:")
        alocacoes = db.execute_query("""
            SELECT fa.id, fa.funcionario_id, fa.empresa_cliente_id, fa.jornada_id, fa.ativo,
                   f.nome_completo as funcionario_nome,
                   j.nome_jornada as jornada_nome
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            LEFT JOIN jornadas_trabalho j ON fa.jornada_id = j.id
            WHERE fa.empresa_cliente_id = %s
        """, (ainexus['id'],))
        
        if alocacoes:
            for a in alocacoes:
                print(f"   👤 {a['funcionario_nome']} (ID: {a['funcionario_id']})")
                print(f"      Jornada: {a['jornada_nome']} (ID: {a['jornada_id']})")
                print(f"      Ativo: {a['ativo']}")
                print()
        else:
            print("   ❌ Nenhum funcionário alocado para AiNexus!")
        
        # 5. Testar a query da API diretamente
        print("5. 🧪 TESTE DA QUERY DA API:")
        api_query = """
        SELECT
            j.id,
            j.nome_jornada as nome,
            j.descricao,
            j.tipo_jornada,
            j.categoria_funcionario,
            j.seg_qui_entrada,
            j.seg_qui_saida,
            j.sexta_entrada,
            j.sexta_saida,
            j.intervalo_inicio,
            j.intervalo_fim,
            j.ativa,
            j.padrao,
            e.razao_social as empresa_nome,
            CONCAT(
                TIME_FORMAT(j.seg_qui_entrada, '%%H:%%i'),
                ' às ',
                TIME_FORMAT(j.seg_qui_saida, '%%H:%%i')
            ) as carga_horaria
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE j.ativa = TRUE AND j.empresa_id = %s
        ORDER BY j.padrao DESC, j.nome_jornada
        """
        
        api_result = db.execute_query(api_query, (ainexus['id'],))
        if api_result:
            print(f"   ✅ API retornaria {len(api_result)} jornadas:")
            for r in api_result:
                print(f"      - {r['nome']} ({r['carga_horaria']})")
        else:
            print("   ❌ API não retornaria nenhuma jornada!")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
