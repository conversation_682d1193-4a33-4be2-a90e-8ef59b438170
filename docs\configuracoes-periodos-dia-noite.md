# 🕐 **CONFIGURAÇÕES DE PERÍODOS DIA/NOITE - RLPONTO-WEB**

**Data:** 21/07/2025  
**Sistema:** RLPONTO-WEB v1.0  
**Objetivo:** Documentar todas as configurações de períodos diurnos e noturnos  
**Status:** ✅ **DOCUMENTAÇÃO COMPLETA**

---

## 📋 **RESUMO EXECUTIVO**

Este documento apresenta todas as configurações de períodos dia/noite do sistema RLPONTO-WEB, incluindo definições globais, períodos específicos, tolerâncias e regras de adicional noturno conforme CLT.

### **🎯 CONFIGURAÇÕES PRINCIPAIS:**
- **Período Diurno:** 06:00 - 21:00 (15 horas)
- **Período Noturno:** 21:00 - 05:59 (8h59min)
- **Adicional Noturno:** 22:00 - 05:00 (CLT)
- **Tolerâncias:** 15 minutos (configurável)

---

## 🗄️ **CONFIGURAÇÕES GLOBAIS DO SISTEMA**

### **📊 Tabela: `configuracoes_sistema` (Categoria: horarios)**

| ID | Chave | Valor | Descrição |
|----|-------|-------|-----------|
| 6 | `morning_start` | 07:00 | Início do período matutino |
| 7 | `morning_end` | 09:30 | Fim do período matutino |
| 8 | `lunch_out_start` | 11:30 | Início da saída para almoço |
| 9 | `lunch_out_end` | 13:30 | Fim da saída para almoço |
| 10 | `lunch_return_start` | 13:30 | Início da volta do almoço |
| 11 | `lunch_return_end` | 15:00 | Fim da volta do almoço |
| 12 | `evening_start` | 17:00 | Início do período vespertino |
| 13 | `evening_end` | 19:00 | Fim do período vespertino |

### **🔧 Uso das Configurações:**
- **Validação de horários:** Sistema usa estes valores para validar pontualidade
- **Cálculos automáticos:** Base para determinação de períodos
- **Interface:** Exibição de horários sugeridos

---

## 🌅 **DEFINIÇÕES DETALHADAS DE PERÍODOS**

### **📊 Tabela: `dia_dados` (Períodos Fixos do Sistema)**

#### **☀️ PERÍODO DIURNO:**

| ID | Período | Início | Fim | Descrição | Ordem |
|----|---------|--------|-----|-----------|-------|
| 8 | Manhã | 06:00 | 11:00 | Período da manhã | 1 |
| 9 | Intervalo | 11:00 | 14:00 | Período de intervalo (almoço) | 2 |
| 10 | Tarde | 14:00 | 18:00 | Período da tarde | 3 |
| 11 | Fim_Diurno | 18:00 | 21:00 | Fim da jornada diurna | 4 |

#### **🌙 PERÍODO NOTURNO:**

| ID | Período | Início | Fim | Descrição | Ordem |
|----|---------|--------|-----|-----------|-------|
| 12 | Noite_Início | 21:00 | 00:00 | Início período noturno | 5 |
| 13 | Noite_Intervalo | 00:00 | 02:00 | Intervalo noturno (jantar) | 6 |
| 14 | Noite_Fim | 02:00 | 05:59 | Fim período noturno | 7 |

### **🎯 Características dos Períodos:**
- **Status:** Todos ativos (`ativo = TRUE`)
- **Ordem:** Sequencial para determinação automática
- **Cobertura:** 24 horas completas (05:59 → 06:00)

---

## 📊 **LINHA DO TEMPO VISUAL (24 HORAS)**

```
🕐 HORÁRIO:  00  02  04  06  08  10  12  14  16  18  20  22  24
            │   │   │   │   │   │   │   │   │   │   │   │   │
🌙 NOTURNO:  ████████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░████
            │Intervalo│                                   │Início
            │00-02    │                                   │21-00
            │         │                                   │
🌅 DIURNO:   ░░░░░░░░████████████████░░░░████████████████████░░░░
            │         │Manhã    │Int│Tarde    │Fim_Diurno │
            │         │06-11    │11-│14-18    │18-21      │
            │         │         │14 │         │           │
```

### **🔍 Legenda:**
- **████** = Período de trabalho
- **░░░░** = Período de intervalo/transição
- **Int** = Intervalo obrigatório

---

## 🏢 **CONFIGURAÇÕES POR EMPRESA**

### **📊 Tabela: `empresas_config` (Horários Específicos)**

#### **🏢 Empresa 4 - Renovar Construção Civil Ltda:**
```sql
PRIMEIRO TURNO:
├── Segunda a Quinta: 07:30 - 17:30
├── Sexta-feira: 07:30 - 16:30
├── Intervalo: 12:00 - 13:00 (almoço)
└── Tolerância: 15 minutos

SEGUNDO TURNO:
├── Segunda a Quinta: 14:00 - 22:00
├── Sexta-feira: 14:00 - 21:00
├── Intervalo: 18:00 - 19:00 (jantar)
├── Status: Inativo
└── Tolerância: 15 minutos
```

#### **🏢 Empresa 11 - AiNexus Tecnologia:**
```sql
PRIMEIRO TURNO:
├── Segunda a Quinta: 08:00 - 18:00
├── Sexta-feira: 08:00 - 17:00
├── Intervalo: 12:00 - 13:00 (almoço)
└── Tolerância: 15 minutos

SEGUNDO TURNO:
├── Segunda a Quinta: 14:00 - 22:00
├── Sexta-feira: 14:00 - 21:00
├── Intervalo: 18:00 - 19:00 (jantar)
├── Status: Inativo
└── Tolerância: 15 minutos
```

---

## 🎯 **JORNADAS EFETIVAS**

### **📊 Tabela: `jornadas_trabalho` (Jornadas Reais)**

#### **✅ PRIMEIRO TURNO (Ativo):**
```sql
Renovar (ID: 2):
├── Nome: "Primeiro Turno"
├── Horário: 07:30-17:30 (seg-qui) / 07:30-16:30 (sexta)
├── Intervalo: 12:00-13:00
├── Padrão: TRUE
└── Ordem: 1

AiNexus (ID: 1):
├── Nome: "Jornada Padrão AiNexus"
├── Horário: 08:00-18:00 (seg-qui) / 08:00-17:00 (sexta)
├── Intervalo: 12:00-13:00
├── Padrão: TRUE
└── Ordem: 0
```

#### **⭐ SEGUNDO TURNO (Ativo):**
```sql
Renovar (ID: 4):
├── Nome: "Segundo Turno"
├── Horário: 14:00-22:00 (seg-qui) / 14:00-21:00 (sexta)
├── Intervalo: 18:00-19:00
├── Padrão: FALSE
└── Ordem: 2

AiNexus (ID: 3):
├── Nome: "Segundo Turno"
├── Horário: 14:00-22:00 (seg-qui) / 14:00-21:00 (sexta)
├── Intervalo: 18:00-19:00
├── Padrão: FALSE
└── Ordem: 2
```

---

## 🌙 **REGRAS DE ADICIONAL NOTURNO**

### **📋 DEFINIÇÕES LEGAIS (CLT):**
- **Período Noturno Legal:** 22:00 - 05:00
- **Adicional Noturno:** 20% sobre a hora normal
- **Hora Noturna Reduzida:** 52 minutos e 30 segundos
- **Base Legal:** Art. 73 da CLT

### **⚠️ DIFERENÇAS NO SISTEMA:**

#### **🔍 Sistema Interno vs CLT:**
```sql
SISTEMA INTERNO (dia_dados):
├── Período: 21:00 - 05:59
├── Uso: Classificação de turnos
└── Finalidade: Organização operacional

CLT LEGAL:
├── Período: 22:00 - 05:00
├── Uso: Cálculo de adicional
└── Finalidade: Cumprimento trabalhista
```

### **💰 CÁLCULO DO ADICIONAL:**
```python
# Exemplo de cálculo
hora_normal = 10.00  # R$ 10,00
adicional_noturno = hora_normal * 0.20  # R$ 2,00
hora_noturna_total = hora_normal + adicional_noturno  # R$ 12,00

# Hora reduzida (52min30s = 0.875 de hora normal)
hora_noturna_reduzida = hora_noturna_total / 0.875  # R$ 13,71
```

---

## ⏰ **TOLERÂNCIAS E CONFIGURAÇÕES**

### **📊 Tolerâncias Globais:**
```sql
CONFIGURAÇÃO PADRÃO:
├── Entrada: 15 minutos
├── Saída: 15 minutos
├── Intervalo: Flexível dentro da janela
└── Configurável por empresa
```

### **🕐 Intervalos Obrigatórios:**
```sql
PERÍODO DIURNO:
├── Duração: 1 hora mínima
├── Janela: 11:00 - 14:00 (3 horas disponíveis)
├── Flexibilidade: Total dentro da janela
└── Validação: Pós-registro

PERÍODO NOTURNO:
├── Duração: 1 hora mínima
├── Janela: 00:00 - 02:00 (2 horas disponíveis)
├── Flexibilidade: Total dentro da janela
└── Validação: Pós-registro
```

---

## 🔧 **CONFIGURAÇÕES TÉCNICAS**

### **📊 Estrutura das Tabelas:**

#### **`configuracoes_sistema`:**
```sql
CREATE TABLE configuracoes_sistema (
    id INT PRIMARY KEY,
    chave VARCHAR(50),
    valor VARCHAR(100),
    descricao TEXT,
    categoria VARCHAR(30)
);
```

#### **`dia_dados`:**
```sql
CREATE TABLE dia_dados (
    id INT PRIMARY KEY,
    turno ENUM('Manha', 'Intervalo', 'Tarde', 'Fim_Diurno', 'Noite_Inicio', 'Noite_Intervalo', 'Noite_Fim'),
    horario_inicio TIME,
    horario_fim TIME,
    descricao VARCHAR(255),
    ativo BOOLEAN,
    ordem_prioridade INT
);
```

#### **`empresas_config`:**
```sql
-- Campos do primeiro turno
jornada_segunda_entrada TIME
jornada_segunda_saida TIME
tolerancia_empresa_minutos INT

-- Campos do segundo turno
segundo_turno_segunda_entrada TIME
segundo_turno_segunda_saida TIME
segundo_turno_ativo BOOLEAN
segundo_turno_tolerancia_minutos INT
```

#### **`jornadas_trabalho`:**
```sql
CREATE TABLE jornadas_trabalho (
    id INT PRIMARY KEY,
    empresa_id INT,
    nome_jornada VARCHAR(100),
    seg_qui_entrada TIME,
    seg_qui_saida TIME,
    sexta_entrada TIME,
    sexta_saida TIME,
    intervalo_inicio TIME,
    intervalo_fim TIME,
    padrao BOOLEAN,
    ativa BOOLEAN,
    ordem_exibicao INT
);
```

---

## 📋 **RESUMO FINAL**

### **✅ CONFIGURAÇÕES DOCUMENTADAS:**
- ✅ **8 configurações globais** em `configuracoes_sistema`
- ✅ **7 períodos detalhados** em `dia_dados`
- ✅ **2 empresas configuradas** em `empresas_config`
- ✅ **4 jornadas efetivas** em `jornadas_trabalho`
- ✅ **Regras de adicional noturno** conforme CLT
- ✅ **Tolerâncias configuráveis** por empresa

### **🎯 COBERTURA COMPLETA:**
- **Período Total:** 24 horas (00:00 - 23:59)
- **Turnos Suportados:** Diurno e Noturno
- **Empresas Ativas:** 2 (Renovar e AiNexus)
- **Jornadas Disponíveis:** 4 (2 por empresa)

### **🚀 SISTEMA OPERACIONAL:**
O RLPONTO-WEB possui configurações completas e detalhadas para todos os períodos do dia, garantindo controle preciso de ponto tanto para trabalho diurno quanto noturno, com total conformidade trabalhista.

---

**📅 Documentação criada em:** 21/07/2025  
**👨‍💻 Elaborado por:** IA Assistant (Augment Agent)  
**🏢 Sistema:** RLPONTO-WEB - Controle de Ponto Biométrico Empresarial  
**📋 Versão:** 1.0 - Documentação Completa
