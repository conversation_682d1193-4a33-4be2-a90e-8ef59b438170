#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        # Buscar empresa AiNexus
        print('=== BUSCANDO EMPRESA AINEXUS ===')
        empresas = db.execute_query("SELECT id, razao_social, empresa_principal FROM empresas WHERE razao_social LIKE %s", ('%AiNexus%',))
        
        if not empresas:
            print("❌ Empresa AiNexus não encontrada!")
            return
            
        for emp in empresas:
            print(f"ID: {emp['id']}, Nome: {emp['razao_social']}, Principal: {emp['empresa_principal']}")
            
            # Buscar jornadas desta empresa
            print(f"\n=== JORNADAS DA EMPRESA {emp['id']} ===")
            jornadas = db.execute_query("SELECT id, nome_jornada, ativa FROM jornadas_trabalho WHERE empresa_id = %s", (emp['id'],))
            
            if jornadas:
                for j in jornadas:
                    print(f"  - ID: {j['id']}, Nome: {j['nome_jornada']}, Ativa: {j['ativa']}")
            else:
                print("  ❌ Nenhuma jornada encontrada")
        
        # Buscar empresa principal
        print('\n=== EMPRESA PRINCIPAL ===')
        emp_principal = db.execute_query("SELECT id, razao_social FROM empresas WHERE empresa_principal = TRUE")
        
        if emp_principal:
            print(f"ID: {emp_principal[0]['id']}, Nome: {emp_principal[0]['razao_social']}")
            
            # Jornadas da empresa principal
            jornadas_principal = db.execute_query("SELECT id, nome_jornada, ativa FROM jornadas_trabalho WHERE empresa_id = %s AND ativa = TRUE", (emp_principal[0]['id'],))
            print(f"Jornadas ativas da empresa principal: {len(jornadas_principal) if jornadas_principal else 0}")
            
            if jornadas_principal:
                for j in jornadas_principal:
                    print(f"  - {j['nome_jornada']}")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")

if __name__ == "__main__":
    main()
