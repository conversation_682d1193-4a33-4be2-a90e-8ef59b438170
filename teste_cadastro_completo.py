#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste Completo - Cadastro de Funcionário via Web
Sistema: RLPONTO-WEB
Data: 20/07/2025
"""

import requests
import json
from datetime import datetime
import re

# Configurações
BASE_URL = "http://10.19.208.31:5000"
LOGIN_URL = f"{BASE_URL}/login"
CADASTRO_URL = f"{BASE_URL}/funcionarios/cadastrar"

def fazer_login():
    """Fazer login no sistema"""
    print("🔐 Fazendo login...")
    
    session = requests.Session()
    
    # Fazer login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=True)
    
    if response.status_code == 200:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print(f"❌ Erro no login: {response.status_code}")
        return None

def testar_cadastro_real():
    """Testar cadastro real de funcionário"""
    print("\n🧪 TESTE: CADASTRO REAL DE FUNCIONÁRIO")
    print("=" * 60)
    
    session = fazer_login()
    if not session:
        return False
    
    timestamp = datetime.now().strftime("%H%M%S")
    
    # Dados completos do funcionário
    dados_funcionario = {
        'nome_completo': f'TESTE REAL {timestamp}',
        'cpf': '000.000.000-00',
        'rg': f'TR-{timestamp}',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'endereco_cep': '00000-000',
        'endereco_estado': 'SP',
        'telefone1': '(11) 99999-9999',
        'empresa_id': '4',
        'cargo': 'TESTE REAL',
        'setor_obra': 'TESTE',
        'matricula_empresa': f'TR{timestamp}',
        'data_admissao': datetime.now().strftime('%Y-%m-%d'),
        'tipo_contrato': 'CLT',
        'status_cadastro': 'Ativo',
        'horas_semanais_obrigatorias': '44.00',
        'nivel_acesso': 'Funcionario'
    }
    
    print(f"📝 Dados do funcionário:")
    print(f"   Nome: {dados_funcionario['nome_completo']}")
    print(f"   Status: {dados_funcionario['status_cadastro']}")
    print(f"   Empresa ID: {dados_funcionario['empresa_id']}")
    print()
    
    # Enviar dados
    print("📤 Enviando dados para o servidor...")
    response = session.post(CADASTRO_URL, data=dados_funcionario, allow_redirects=False)
    
    print(f"📊 Status da resposta: {response.status_code}")
    
    if response.status_code == 302:
        # Redirect - sucesso
        location = response.headers.get('Location', '')
        print(f"✅ Redirect para: {location}")
        
        if '/funcionarios/' in location and '/detalhes' in location:
            # Extrair ID do funcionário
            try:
                funcionario_id = location.split('/funcionarios/')[1].split('/')[0]
                print(f"✅ Funcionário criado com ID: {funcionario_id}")
                
                # Verificar status do funcionário
                return verificar_status_funcionario(session, funcionario_id)
                
            except Exception as e:
                print(f"⚠️ Erro ao extrair ID: {e}")
                return False
        else:
            print(f"⚠️ Redirect inesperado: {location}")
            return False
    else:
        print(f"❌ Erro no cadastro: {response.status_code}")
        
        # Verificar se há mensagens de erro
        content = response.text
        if 'alert-danger' in content or 'error' in content.lower():
            print("🔍 Procurando mensagens de erro...")
            
            # Extrair mensagens de erro
            error_patterns = [
                r'<div[^>]*alert-danger[^>]*>(.*?)</div>',
                r'<li[^>]*class="[^"]*error[^"]*"[^>]*>(.*?)</li>',
            ]
            
            for pattern in error_patterns:
                matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    clean_error = re.sub(r'<[^>]+>', '', match).strip()
                    if clean_error and len(clean_error) > 5:
                        print(f"❌ Erro: {clean_error}")
        
        return False

def verificar_status_funcionario(session, funcionario_id):
    """Verificar status do funcionário cadastrado"""
    print(f"\n🔍 VERIFICANDO STATUS DO FUNCIONÁRIO ID: {funcionario_id}")
    print("=" * 60)
    
    detalhes_url = f"{BASE_URL}/funcionarios/{funcionario_id}"
    response = session.get(detalhes_url)
    
    if response.status_code == 200:
        content = response.text
        
        # Procurar pelo status na página
        if 'Status:</strong> Ativo' in content or 'badge-success' in content:
            print("✅ STATUS: ATIVO")
            return True
        elif 'Status:</strong> Inativo' in content or 'badge-danger' in content:
            print("❌ STATUS: INATIVO")
            return False
        else:
            # Procurar por padrões alternativos
            status_patterns = [
                r'Status[^>]*>([^<]+)<',
                r'status[^>]*>([^<]+)<',
                r'badge[^>]*>([^<]+)<'
            ]
            
            for pattern in status_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    clean_status = match.strip()
                    if 'ativo' in clean_status.lower():
                        if 'inativo' in clean_status.lower():
                            print(f"❌ STATUS: {clean_status}")
                            return False
                        else:
                            print(f"✅ STATUS: {clean_status}")
                            return True
            
            print("❓ Status não identificado na página")
            # Salvar página para debug
            with open(f'debug_funcionario_{funcionario_id}.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📄 Página salva em: debug_funcionario_{funcionario_id}.html")
            return None
    else:
        print(f"❌ Erro ao acessar detalhes: {response.status_code}")
        return None

def main():
    """Função principal"""
    print("🧪 TESTE COMPLETO: CADASTRO DE FUNCIONÁRIO VIA WEB")
    print("=" * 70)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Testar cadastro
    sucesso = testar_cadastro_real()
    
    print("\n" + "=" * 70)
    print("📊 RESULTADO FINAL:")
    
    if sucesso is True:
        print("✅ SUCESSO: Funcionário cadastrado com status ATIVO")
        print("🎉 PROBLEMA RESOLVIDO!")
    elif sucesso is False:
        print("❌ FALHA: Funcionário cadastrado com status INATIVO")
        print("🚨 PROBLEMA PERSISTE!")
    else:
        print("❓ INCONCLUSIVO: Não foi possível determinar o resultado")
        print("🔍 NECESSÁRIA INVESTIGAÇÃO MANUAL")
    
    print("\n✅ TESTE CONCLUÍDO")

if __name__ == "__main__":
    main()
