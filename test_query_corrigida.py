#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== TESTE DA QUERY CORRIGIDA ===\n")
        
        # Query corrigida que agora está no código
        sql_func = """
        SELECT COUNT(DISTINCT funcionario_id) as count FROM (
            -- Funcionários alocados via tabela funcionario_alocacoes
            SELECT fa.funcionario_id
            FROM funcionario_alocacoes fa
            WHERE fa.empresa_cliente_id = %s AND fa.ativo = TRUE

            UNION

            -- Funcionários cadastrados diretamente na empresa
            SELECT f.id as funcionario_id
            FROM funcionarios f
            WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        ) as todos_funcionarios
        """
        
        # Usar empresa_cliente_id = 11 para ambos os parâmetros
        empresa_cliente_id = 11
        
        result_func = db.execute_query(sql_func, (empresa_cliente_id, empresa_cliente_id))
        contagem = result_func[0]['count'] if result_func else 0
        
        print(f"Empresa Cliente ID usado: {empresa_cliente_id}")
        print(f"Resultado da contagem: {contagem}")
        
        # Verificar cada parte separadamente
        print(f"\n🔍 ANÁLISE DETALHADA:")
        
        # Parte 1: Funcionários alocados
        parte1 = db.execute_query("""
            SELECT fa.funcionario_id, f.nome_completo
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            WHERE fa.empresa_cliente_id = %s AND fa.ativo = TRUE
        """, (empresa_cliente_id,))
        
        print(f"Parte 1 (alocados via empresa_cliente_id={empresa_cliente_id}): {len(parte1)} funcionários")
        for p1 in parte1:
            print(f"  - ID {p1['funcionario_id']}: {p1['nome_completo']}")
        
        # Parte 2: Funcionários cadastrados
        parte2 = db.execute_query("""
            SELECT f.id as funcionario_id, f.nome_completo
            FROM funcionarios f
            WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        """, (empresa_cliente_id,))
        
        print(f"Parte 2 (cadastrados na empresa_id={empresa_cliente_id}): {len(parte2)} funcionários")
        for p2 in parte2:
            print(f"  - ID {p2['funcionario_id']}: {p2['nome_completo']}")
        
        # Verificar duplicação
        ids_parte1 = set([p['funcionario_id'] for p in parte1])
        ids_parte2 = set([p['funcionario_id'] for p in parte2])
        
        duplicados = ids_parte1.intersection(ids_parte2)
        if duplicados:
            print(f"\n❌ FUNCIONÁRIOS DUPLICADOS: {duplicados}")
            print("Isso explica por que a contagem estava errada!")
        else:
            print(f"\n✅ Nenhuma duplicação encontrada")
        
        print(f"Total único (DISTINCT): {len(ids_parte1.union(ids_parte2))}")
        print(f"Contagem da query: {contagem}")
        
        if len(ids_parte1.union(ids_parte2)) == contagem:
            print("✅ Query funcionando corretamente!")
        else:
            print("❌ Ainda há problema na query!")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
