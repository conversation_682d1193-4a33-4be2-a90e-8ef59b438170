# RLPONTO-WEB Agent Guide

## Commands
- **Run server**: `python3 app.py` (main Flask application)
- **Test single file**: `python test_<filename>.py` (manual test scripts)
- **Deploy**: Use `deploy_restart.py` or `restart_flask.py` for remote deployment
- **Check status**: `ps aux | grep python | grep app.py`
- **Kill server**: `pkill -f 'python.*app.py'`

## Architecture
- **Main app**: `app.py` - Flask application with biometric verification APIs
- **Database**: MySQL with PyMySQL connector, schema in `db/controle_ponto.sql`
- **Biometric bridge**: `zkagent/` - Java-based ZK4500 fingerprint scanner integration
- **Templates**: `templates/` - Jinja2 HTML templates
- **Static files**: `static/js/` - Frontend JavaScript
- **Tests**: Manual test scripts (`test_*.py`, `debug_*.py`) - no automated test framework

## Code Style
- **Imports**: Standard library first, then third-party (Flask, PyMySQL), then local
- **Functions**: Snake_case naming, descriptive names with action verbs
- **Database**: Use PyMySQL with proper connection handling and error catching
- **APIs**: RESTful endpoints with `/api/` prefix, JSON responses with `{'success': bool, 'data': any}`
- **Security**: Hash passwords with hashlib, validate inputs, use security headers
- **Logging**: Use Python logging module for debugging and error tracking
- **Error handling**: Try/catch blocks with meaningful error messages and HTTP status codes
