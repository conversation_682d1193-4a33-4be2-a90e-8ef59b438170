#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_nova_api():
    """Testar nova API de funcionários disponíveis por cliente"""
    
    print("=== TESTE NOVA API FUNCIONÁRIOS DISPONÍVEIS ===\n")
    
    # Fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    login_response = session.post('http://************/login', data=login_data)
    
    print(f"1. Login status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ Falha no login")
        return
    
    # Testar API antiga (todos os funcionários)
    print(f"\n2. Testando API antiga (todos os funcionários):")
    api_antiga = session.get('http://************/empresa-principal/api/funcionarios-disponiveis')
    
    if api_antiga.status_code == 200:
        data_antiga = api_antiga.json()
        print(f"   ✅ Status: {api_antiga.status_code}")
        print(f"   Total funcionários: {len(data_antiga.get('funcionarios', []))}")
        for func in data_antiga.get('funcionarios', []):
            print(f"     - {func['nome_completo']} (ID: {func['id']})")
    else:
        print(f"   ❌ Erro: {api_antiga.status_code}")
    
    # Testar nova API (funcionários disponíveis para AiNexus)
    print(f"\n3. Testando nova API (funcionários disponíveis para AiNexus - ID 11):")
    api_nova = session.get('http://************/empresa-principal/api/funcionarios-disponiveis/11')
    
    if api_nova.status_code == 200:
        data_nova = api_nova.json()
        print(f"   ✅ Status: {api_nova.status_code}")
        print(f"   Success: {data_nova.get('success')}")
        print(f"   Funcionários disponíveis para AiNexus: {len(data_nova.get('funcionarios', []))}")
        
        funcionarios_disponiveis = data_nova.get('funcionarios', [])
        if funcionarios_disponiveis:
            for func in funcionarios_disponiveis:
                print(f"     - {func['nome_completo']} (ID: {func['id']})")
        else:
            print("     ✅ Nenhum funcionário disponível (todos já alocados)")
    else:
        print(f"   ❌ Erro: {api_nova.status_code}")
        print(f"   Resposta: {api_nova.text[:200]}")
    
    # Verificar diferença
    if api_antiga.status_code == 200 and api_nova.status_code == 200:
        total_antiga = len(data_antiga.get('funcionarios', []))
        total_nova = len(data_nova.get('funcionarios', []))
        
        print(f"\n4. Comparação:")
        print(f"   API antiga (todos): {total_antiga} funcionários")
        print(f"   API nova (disponíveis): {total_nova} funcionários")
        print(f"   Diferença: {total_antiga - total_nova} funcionários já alocados")
        
        if total_nova < total_antiga:
            print("   ✅ Filtro funcionando corretamente!")
        else:
            print("   ⚠️ Filtro pode não estar funcionando")

if __name__ == "__main__":
    test_nova_api()
