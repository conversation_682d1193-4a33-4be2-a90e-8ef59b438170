#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== DEBUG CONTAGEM FUNCIONÁRIOS AINEXUS ===\n")
        
        # 1. Verificar dados básicos da AiNexus
        print("1. 🏢 DADOS BÁSICOS AINEXUS:")
        ainexus = db.execute_query("""
            SELECT id, razao_social, nome_fantasia, cnpj
            FROM empresas 
            WHERE id = 11
        """)
        
        if ainexus:
            empresa = ainexus[0]
            print(f"   ID: {empresa['id']}")
            print(f"   Razão Social: {empresa['razao_social']}")
            print(f"   Nome Fantasia: {empresa['nome_fantasia']}")
            print(f"   CNPJ: {empresa['cnpj']}")
        else:
            print("   ❌ AiNexus não encontrada!")
            return
        
        # 2. Verificar registro na tabela empresa_clientes
        print(f"\n2. 📋 REGISTRO COMO CLIENTE:")
        cliente_registro = db.execute_query("""
            SELECT id, empresa_principal_id, empresa_cliente_id, status_contrato
            FROM empresa_clientes 
            WHERE empresa_cliente_id = 11
        """)
        
        if cliente_registro:
            cliente = cliente_registro[0]
            print(f"   ID do contrato: {cliente['id']}")
            print(f"   Empresa principal: {cliente['empresa_principal_id']}")
            print(f"   Empresa cliente: {cliente['empresa_cliente_id']}")
            print(f"   Status: {cliente['status_contrato']}")
        else:
            print("   ❌ AiNexus não está registrada como cliente!")
            return
        
        # 3. Verificar funcionários cadastrados diretamente na AiNexus
        print(f"\n3. 👥 FUNCIONÁRIOS CADASTRADOS NA AINEXUS:")
        funcionarios_diretos = db.execute_query("""
            SELECT id, nome_completo, status_cadastro, empresa_id
            FROM funcionarios 
            WHERE empresa_id = 11 AND status_cadastro = 'Ativo'
        """)
        
        print(f"   Total de funcionários ativos: {len(funcionarios_diretos)}")
        for func in funcionarios_diretos:
            print(f"   - ID {func['id']}: {func['nome_completo']} (Status: {func['status_cadastro']})")
        
        # 4. Verificar funcionários alocados via tabela funcionario_alocacoes
        print(f"\n4. 🔗 FUNCIONÁRIOS ALOCADOS VIA TABELA:")
        funcionarios_alocados = db.execute_query("""
            SELECT fa.id, fa.funcionario_id, f.nome_completo, fa.ativo, fa.data_inicio, fa.data_fim
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            WHERE fa.empresa_cliente_id = 11 AND fa.ativo = 1
        """)
        
        print(f"   Total de alocações ativas: {len(funcionarios_alocados)}")
        for aloc in funcionarios_alocados:
            print(f"   - Alocação ID {aloc['id']}: Funcionário {aloc['funcionario_id']} ({aloc['nome_completo']})")
            print(f"     Período: {aloc['data_inicio']} até {aloc['data_fim'] or 'Em aberto'}")
        
        # 5. Simular a query que está sendo usada na interface
        print(f"\n5. 🧮 SIMULANDO QUERY DA INTERFACE:")
        
        # Query original que está causando o problema
        query_interface = """
        SELECT COUNT(DISTINCT funcionario_id) as count FROM (
            -- Funcionários alocados via tabela funcionario_alocacoes
            SELECT fa.funcionario_id
            FROM funcionario_alocacoes fa
            WHERE fa.contrato_id = %s AND fa.ativo = TRUE

            UNION

            -- Funcionários cadastrados diretamente na empresa
            SELECT f.id as funcionario_id
            FROM funcionarios f
            WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        ) as todos_funcionarios
        """
        
        # Usar o ID do contrato (não da empresa)
        contrato_id = cliente['id']
        empresa_id = 11
        
        resultado_interface = db.execute_query(query_interface, (contrato_id, empresa_id))
        contagem_interface = resultado_interface[0]['count'] if resultado_interface else 0
        
        print(f"   Contrato ID usado: {contrato_id}")
        print(f"   Empresa ID usado: {empresa_id}")
        print(f"   Resultado da contagem: {contagem_interface}")
        
        # 6. Verificar cada parte da UNION separadamente
        print(f"\n6. 🔍 ANÁLISE DETALHADA DA UNION:")
        
        # Parte 1: Funcionários alocados
        parte1 = db.execute_query("""
            SELECT fa.funcionario_id, f.nome_completo
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            WHERE fa.contrato_id = %s AND fa.ativo = TRUE
        """, (contrato_id,))
        
        print(f"   Parte 1 (alocados via contrato_id={contrato_id}): {len(parte1)} funcionários")
        for p1 in parte1:
            print(f"     - ID {p1['funcionario_id']}: {p1['nome_completo']}")
        
        # Parte 2: Funcionários cadastrados
        parte2 = db.execute_query("""
            SELECT f.id as funcionario_id, f.nome_completo
            FROM funcionarios f
            WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        """, (empresa_id,))
        
        print(f"   Parte 2 (cadastrados na empresa_id={empresa_id}): {len(parte2)} funcionários")
        for p2 in parte2:
            print(f"     - ID {p2['funcionario_id']}: {p2['nome_completo']}")
        
        # 7. Verificar se há duplicação
        print(f"\n7. ⚠️ VERIFICAÇÃO DE DUPLICAÇÃO:")
        ids_parte1 = set([p['funcionario_id'] for p in parte1])
        ids_parte2 = set([p['funcionario_id'] for p in parte2])
        
        duplicados = ids_parte1.intersection(ids_parte2)
        if duplicados:
            print(f"   ❌ FUNCIONÁRIOS DUPLICADOS: {duplicados}")
            print("   Isso explica a contagem incorreta!")
        else:
            print(f"   ✅ Nenhuma duplicação encontrada")
        
        print(f"\n   Total único (sem duplicação): {len(ids_parte1.union(ids_parte2))}")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
