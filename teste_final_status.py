#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste Final - Status do Funcionário
Sistema: RLPONTO-WEB
Data: 20/07/2025
"""

import requests
from datetime import datetime

# Configurações
BASE_URL = "http://10.19.208.31:5000"
LOGIN_URL = f"{BASE_URL}/login"
CADASTRO_URL = f"{BASE_URL}/funcionarios/cadastrar"

def fazer_login():
    """Fazer login no sistema"""
    print("🔐 Fazendo login...")
    
    session = requests.Session()
    
    # Fazer login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=True)
    
    if response.status_code == 200:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print(f"❌ Erro no login: {response.status_code}")
        return None

def testar_cadastro_minimo():
    """Testar cadastro com campos mínimos obrigatórios"""
    print("\n🧪 TESTE: CADASTRO COM CAMPOS MÍNIMOS")
    print("=" * 60)
    
    session = fazer_login()
    if not session:
        return False
    
    timestamp = datetime.now().strftime("%H%M%S")
    
    # Dados MÍNIMOS obrigatórios
    dados_minimos = {
        'nome_completo': f'TESTE MINIMO {timestamp}',
        'cpf': '000.000.000-00',
        'rg': f'TM-{timestamp}',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'endereco_cep': '00000-000',
        'endereco_estado': 'SP',
        'telefone1': '(11) 99999-9999',
        'empresa_id': '4',
        'cargo': 'TESTE MINIMO',
        'setor_obra': 'TESTE',
        'matricula_empresa': f'TM{timestamp}',
        'data_admissao': datetime.now().strftime('%Y-%m-%d'),
        'tipo_contrato': 'CLT',
        'status_cadastro': 'Ativo',  # EXPLICITAMENTE ATIVO
        'horas_semanais_obrigatorias': '44.00',
        'nivel_acesso': 'Funcionario'
    }
    
    print(f"📝 Dados mínimos:")
    print(f"   Nome: {dados_minimos['nome_completo']}")
    print(f"   Status: {dados_minimos['status_cadastro']}")
    print(f"   Empresa ID: {dados_minimos['empresa_id']}")
    print()
    
    # Enviar dados
    print("📤 Enviando dados mínimos...")
    response = session.post(CADASTRO_URL, data=dados_minimos, allow_redirects=False)
    
    print(f"📊 Status da resposta: {response.status_code}")
    
    if response.status_code == 302:
        # Redirect - sucesso
        location = response.headers.get('Location', '')
        print(f"✅ Redirect para: {location}")
        
        if '/funcionarios/' in location and '/detalhes' in location:
            # Extrair ID do funcionário
            try:
                funcionario_id = location.split('/funcionarios/')[1].split('/')[0]
                print(f"✅ Funcionário criado com ID: {funcionario_id}")
                return True
                
            except Exception as e:
                print(f"⚠️ Erro ao extrair ID: {e}")
                return False
        else:
            print(f"⚠️ Redirect inesperado: {location}")
            return False
    else:
        print(f"❌ Erro no cadastro: {response.status_code}")
        
        # Salvar resposta para debug
        with open(f'debug_erro_{timestamp}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"📄 Resposta salva em: debug_erro_{timestamp}.html")
        
        return False

def main():
    """Função principal"""
    print("🧪 TESTE FINAL: STATUS DO FUNCIONÁRIO")
    print("=" * 70)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Testar cadastro mínimo
    sucesso = testar_cadastro_minimo()
    
    print("\n" + "=" * 70)
    print("📊 RESULTADO FINAL:")
    
    if sucesso:
        print("✅ SUCESSO: Funcionário cadastrado com sucesso")
        print("🎉 PROBLEMA RESOLVIDO!")
    else:
        print("❌ FALHA: Ainda há problemas no cadastro")
        print("🔍 VERIFICAR ARQUIVO DE DEBUG")
    
    print("\n✅ TESTE CONCLUÍDO")

if __name__ == "__main__":
    main()
