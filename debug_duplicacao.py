#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== DEBUG DUPLICAÇÃO TESTE MOV 2 ===\n")
        
        # 1. Verificar funcionários com nome similar
        print("1. 🔍 FUNCIONÁRIOS COM NOME TESTE MOV:")
        funcionarios_teste = db.execute_query("""
            SELECT id, nome_completo, status_cadastro, empresa_id, ativo
            FROM funcionarios 
            WHERE nome_completo LIKE %s
            ORDER BY id
        """, ('%TESTE MOV%',))
        
        for func in funcionarios_teste:
            print(f"   ID: {func['id']} | Nome: {func['nome_completo']} | Status: {func['status_cadastro']} | Empresa: {func['empresa_id']} | Ativo: {func['ativo']}")
        
        # 2. Verificar query da API antiga
        print(f"\n2. 📋 QUERY API ANTIGA (get_funcionarios_disponiveis):")
        
        # Simular a query da função get_funcionarios_disponiveis
        empresa_principal_id = 4  # ID da empresa principal
        
        funcionarios_api = db.execute_query("""
            SELECT f.id, f.nome_completo, f.cpf, f.cargo, f.setor, f.status_cadastro,
                   f.data_admissao, f.empresa_id,
                   CASE WHEN fa.id IS NOT NULL THEN TRUE ELSE FALSE END as ja_alocado,
                   fa.empresa_cliente_id as cliente_atual_id,
                   ec.razao_social as cliente_atual_nome,
                   ef.razao_social as empresa_nome
            FROM funcionarios f
            LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
            LEFT JOIN empresas ec ON fa.empresa_cliente_id = ec.id
            LEFT JOIN empresas ef ON f.empresa_id = ef.id
            WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
            ORDER BY f.nome_completo
        """, (empresa_principal_id,))
        
        print(f"   Total resultados: {len(funcionarios_api)}")
        
        # Agrupar por ID para ver duplicações
        funcionarios_por_id = {}
        for func in funcionarios_api:
            func_id = func['id']
            if func_id not in funcionarios_por_id:
                funcionarios_por_id[func_id] = []
            funcionarios_por_id[func_id].append(func)
        
        print(f"\n3. 🔍 ANÁLISE DE DUPLICAÇÃO:")
        for func_id, registros in funcionarios_por_id.items():
            if len(registros) > 1:
                print(f"   ❌ DUPLICADO - ID {func_id}: {registros[0]['nome_completo']}")
                for i, reg in enumerate(registros):
                    print(f"      Registro {i+1}: Cliente atual: {reg['cliente_atual_nome']} (ID: {reg['cliente_atual_id']})")
            else:
                print(f"   ✅ ÚNICO - ID {func_id}: {registros[0]['nome_completo']}")
        
        # 4. Verificar alocações do TESTE MOV 2
        if funcionarios_teste:
            teste_id = funcionarios_teste[0]['id']
            print(f"\n4. 🔗 ALOCAÇÕES DO TESTE MOV 2 (ID {teste_id}):")
            
            alocacoes = db.execute_query("""
                SELECT fa.id, fa.empresa_cliente_id, fa.ativo, fa.data_inicio, fa.data_fim,
                       e.razao_social as empresa_nome
                FROM funcionario_alocacoes fa
                JOIN empresas e ON fa.empresa_cliente_id = e.id
                WHERE fa.funcionario_id = %s
                ORDER BY fa.id
            """, (teste_id,))
            
            if alocacoes:
                print(f"   Total alocações: {len(alocacoes)}")
                for aloc in alocacoes:
                    status = "ATIVA" if aloc['ativo'] else "INATIVA"
                    print(f"     - ID {aloc['id']}: {aloc['empresa_nome']} | Status: {status} | Período: {aloc['data_inicio']} até {aloc['data_fim'] or 'Em aberto'}")
            else:
                print(f"   ✅ Nenhuma alocação encontrada")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
