#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste CPF Válido - Status do Funcionário
Sistema: RLPONTO-WEB
Data: 20/07/2025
"""

import requests
from datetime import datetime

# Configurações
BASE_URL = "http://10.19.208.31:5000"
LOGIN_URL = f"{BASE_URL}/login"
CADASTRO_URL = f"{BASE_URL}/funcionarios/cadastrar"

def fazer_login():
    """Fazer login no sistema"""
    print("🔐 Fazendo login...")
    
    session = requests.Session()
    
    # Fazer login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=True)
    
    if response.status_code == 200:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print(f"❌ Erro no login: {response.status_code}")
        return None

def testar_cadastro_cpf_valido():
    """Testar cadastro com CPF válido"""
    print("\n🧪 TESTE: CADASTRO COM CPF VÁLIDO")
    print("=" * 60)
    
    session = fazer_login()
    if not session:
        return False
    
    timestamp = datetime.now().strftime("%H%M%S")
    
    # Dados com CPF VÁLIDO
    dados_validos = {
        'nome_completo': f'TESTE CPF VALIDO {timestamp}',
        'cpf': '123.456.789-09',  # CPF VÁLIDO
        'rg': f'TV-{timestamp}',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'endereco_cep': '01310-100',  # CEP válido
        'endereco_estado': 'SP',
        'telefone1': '(11) 99999-9999',
        'empresa_id': '4',
        'cargo': 'TESTE CPF VALIDO',
        'setor_obra': 'TESTE',
        'matricula_empresa': f'TV{timestamp}',
        'data_admissao': datetime.now().strftime('%Y-%m-%d'),
        'tipo_contrato': 'CLT',
        'status_cadastro': 'Ativo',  # EXPLICITAMENTE ATIVO
        'horas_semanais_obrigatorias': '44.00',
        'nivel_acesso': 'Funcionario'
    }
    
    print(f"📝 Dados com CPF válido:")
    print(f"   Nome: {dados_validos['nome_completo']}")
    print(f"   CPF: {dados_validos['cpf']}")
    print(f"   Status: {dados_validos['status_cadastro']}")
    print(f"   Empresa ID: {dados_validos['empresa_id']}")
    print()
    
    # Enviar dados
    print("📤 Enviando dados com CPF válido...")
    response = session.post(CADASTRO_URL, data=dados_validos, allow_redirects=False)
    
    print(f"📊 Status da resposta: {response.status_code}")
    
    if response.status_code == 302:
        # Redirect - sucesso
        location = response.headers.get('Location', '')
        print(f"✅ Redirect para: {location}")
        
        if '/funcionarios/' in location and '/detalhes' in location:
            # Extrair ID do funcionário
            try:
                funcionario_id = location.split('/funcionarios/')[1].split('/')[0]
                print(f"✅ Funcionário criado com ID: {funcionario_id}")
                
                # Verificar status do funcionário no banco
                return verificar_status_banco(funcionario_id)
                
            except Exception as e:
                print(f"⚠️ Erro ao extrair ID: {e}")
                return False
        else:
            print(f"⚠️ Redirect inesperado: {location}")
            return False
    else:
        print(f"❌ Erro no cadastro: {response.status_code}")
        
        # Salvar resposta para debug
        with open(f'debug_cpf_valido_{timestamp}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"📄 Resposta salva em: debug_cpf_valido_{timestamp}.html")
        
        return False

def verificar_status_banco(funcionario_id):
    """Verificar status do funcionário diretamente no banco"""
    print(f"\n🔍 VERIFICANDO STATUS NO BANCO - ID: {funcionario_id}")
    print("=" * 60)
    
    import subprocess
    
    try:
        # Consultar status no banco
        cmd = f'ssh rlponto-server "mysql -u cavalcrod -p200381 controle_ponto -e \\"SELECT id, nome_completo, status_cadastro FROM funcionarios WHERE id = {funcionario_id};\\" 2>/dev/null"'
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            lines = output.split('\n')
            
            if len(lines) >= 2:
                # Primeira linha são os cabeçalhos, segunda linha são os dados
                dados = lines[1].split('\t')
                if len(dados) >= 3:
                    id_func = dados[0]
                    nome = dados[1]
                    status = dados[2]
                    
                    print(f"✅ Funcionário encontrado:")
                    print(f"   ID: {id_func}")
                    print(f"   Nome: {nome}")
                    print(f"   Status: {status}")
                    
                    if status == 'Ativo':
                        print("🎉 STATUS CORRETO: ATIVO!")
                        return True
                    else:
                        print(f"❌ STATUS INCORRETO: {status}")
                        return False
                else:
                    print("❌ Dados incompletos retornados do banco")
                    return False
            else:
                print("❌ Funcionário não encontrado no banco")
                return False
        else:
            print(f"❌ Erro ao consultar banco: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE CPF VÁLIDO: STATUS DO FUNCIONÁRIO")
    print("=" * 70)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Testar cadastro com CPF válido
    sucesso = testar_cadastro_cpf_valido()
    
    print("\n" + "=" * 70)
    print("📊 RESULTADO FINAL:")
    
    if sucesso:
        print("✅ SUCESSO: Funcionário cadastrado com status ATIVO")
        print("🎉 PROBLEMA RESOLVIDO!")
        print("🔧 CORREÇÃO: Campo EPI obrigatório removido")
    else:
        print("❌ FALHA: Ainda há problemas no cadastro")
        print("🔍 VERIFICAR LOGS E ARQUIVOS DE DEBUG")
    
    print("\n✅ TESTE CONCLUÍDO")

if __name__ == "__main__":
    main()
