#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def main():
    try:
        db = DatabaseManager()
        
        print("=== ANÁLISE DETALHADA DOS TIPOS DE JORNADA ===\n")
        
        # 1. Verificar todas as jornadas da AiNexus com detalhes completos
        print("1. 🏢 JORNADAS DA AINEXUS - ANÁLISE COMPLETA:")
        jornadas = db.execute_query("""
            SELECT id, nome_jornada, tipo_jornada, categoria_funcionario,
                   seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
                   intervalo_inicio, intervalo_fim, ativa, padrao, empresa_id
            FROM jornadas_trabalho 
            WHERE empresa_id = 11
            ORDER BY padrao DESC, nome_jornada
        """)
        
        for j in jornadas:
            print(f"\n   📋 JORNADA ID: {j['id']}")
            print(f"      Nome: {j['nome_jornada']}")
            print(f"      Tipo: {j['tipo_jornada']} ← VERIFICAR ESTE CAMPO")
            print(f"      Categoria: {j['categoria_funcionario']}")
            print(f"      Horários Seg-Qui: {j['seg_qui_entrada']} às {j['seg_qui_saida']}")
            print(f"      Horários Sexta: {j['sexta_entrada']} às {j['sexta_saida']}")
            print(f"      Intervalo: {j['intervalo_inicio']} às {j['intervalo_fim']}")
            print(f"      Ativa: {j['ativa']} | Padrão: {j['padrao']}")
            
            # Análise lógica do tipo baseado no horário
            entrada_hora = int(str(j['seg_qui_entrada']).split(':')[0])
            saida_hora = int(str(j['seg_qui_saida']).split(':')[0])
            
            if entrada_hora >= 6 and saida_hora <= 18:
                tipo_sugerido = "Diurno"
            elif entrada_hora >= 14 and saida_hora >= 22:
                tipo_sugerido = "Noturno"
            elif entrada_hora >= 22 or saida_hora <= 6:
                tipo_sugerido = "Madrugada"
            else:
                tipo_sugerido = "Misto"
            
            print(f"      🔍 ANÁLISE: Baseado no horário ({j['seg_qui_entrada']}-{j['seg_qui_saida']})")
            print(f"          Tipo atual: {j['tipo_jornada']}")
            print(f"          Tipo sugerido: {tipo_sugerido}")
            
            if j['tipo_jornada'] != tipo_sugerido:
                print(f"          ⚠️ INCONSISTÊNCIA DETECTADA!")
            else:
                print(f"          ✅ Tipo correto")
        
        # 2. Verificar todos os tipos de jornada disponíveis no sistema
        print(f"\n2. 📊 TODOS OS TIPOS DE JORNADA NO SISTEMA:")
        tipos = db.execute_query("""
            SELECT DISTINCT tipo_jornada, COUNT(*) as quantidade
            FROM jornadas_trabalho 
            GROUP BY tipo_jornada
            ORDER BY quantidade DESC
        """)
        
        for t in tipos:
            print(f"   - {t['tipo_jornada']}: {t['quantidade']} jornadas")
        
        # 3. Verificar estrutura da tabela para ver valores permitidos
        print(f"\n3. 🔧 ESTRUTURA DO CAMPO tipo_jornada:")
        estrutura = db.execute_query("DESCRIBE jornadas_trabalho")
        
        for campo in estrutura:
            if campo['Field'] == 'tipo_jornada':
                print(f"   Campo: {campo['Field']}")
                print(f"   Tipo: {campo['Type']}")
                print(f"   Nulo: {campo['Null']}")
                print(f"   Padrão: {campo['Default']}")
                
                # Se for ENUM, extrair valores permitidos
                if 'enum' in campo['Type'].lower():
                    valores = campo['Type'].replace('enum(', '').replace(')', '').replace("'", "").split(',')
                    print(f"   Valores permitidos: {valores}")
        
        # 4. Verificar se há outras empresas com jornadas similares
        print(f"\n4. 🏢 COMPARAÇÃO COM OUTRAS EMPRESAS:")
        outras_jornadas = db.execute_query("""
            SELECT j.nome_jornada, j.tipo_jornada, j.seg_qui_entrada, j.seg_qui_saida,
                   e.razao_social as empresa_nome
            FROM jornadas_trabalho j
            JOIN empresas e ON j.empresa_id = e.id
            WHERE j.empresa_id != 11 AND j.ativa = TRUE
            ORDER BY j.seg_qui_entrada
        """)
        
        for oj in outras_jornadas:
            entrada_hora = int(str(oj['seg_qui_entrada']).split(':')[0])
            saida_hora = int(str(oj['seg_qui_saida']).split(':')[0])
            
            print(f"   {oj['empresa_nome']}: {oj['nome_jornada']}")
            print(f"      Horário: {oj['seg_qui_entrada']}-{oj['seg_qui_saida']} | Tipo: {oj['tipo_jornada']}")
            
            # Comparar com padrões similares
            if entrada_hora >= 14 and saida_hora >= 22:
                print(f"      🔍 Padrão similar ao 'Segundo Turno' da AiNexus")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
