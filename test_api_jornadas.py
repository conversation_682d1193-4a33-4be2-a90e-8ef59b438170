#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
from datetime import datetime, date, time
from decimal import Decimal
import json

def test_jornadas_api(empresa_id):
    """Simular a função get_jornadas_por_empresa"""
    try:
        print(f"🔍 [DEBUG] Buscando jornadas para empresa ID: {empresa_id}")
        db = DatabaseManager()

        # Buscar jornadas da empresa cliente específica
        sql = """
        SELECT
            j.id,
            j.nome_jornada as nome,
            j.descricao,
            j.tipo_jornada,
            j.categoria_funcionario,
            j.seg_qui_entrada,
            j.seg_qui_saida,
            j.sexta_entrada,
            j.sexta_saida,
            j.intervalo_inicio,
            j.intervalo_fim,
            j.ativa,
            j.padra<PERSON>,
            e.razao_social as empresa_nome,
            CONCAT(
                TIME_FORMAT(j.seg_qui_entrada, '%%H:%%i'),
                ' às ',
                TIME_FORMAT(j.seg_qui_saida, '%%H:%%i')
            ) as carga_horaria
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE j.ativa = TRUE AND j.empresa_id = %s
        ORDER BY j.padrao DESC, j.nome_jornada
        """

        jornadas = db.execute_query(sql, (empresa_id,))
        print(f"📊 [DEBUG] Encontradas {len(jornadas) if jornadas else 0} jornadas para empresa {empresa_id}")
        
        if jornadas:
            print(f"📊 [DEBUG] Jornadas encontradas: {[j.get('nome', 'N/A') for j in jornadas]}")
            
            # Converter tipos não serializáveis
            for jornada in jornadas:
                print(f"  🔧 Processando jornada: {jornada.get('nome', 'N/A')}")
                for key, value in jornada.items():
                    if isinstance(value, (date, datetime, time)):
                        jornada[key] = value.isoformat() if hasattr(value, 'isoformat') else str(value)
                        print(f"    ⏰ Convertido {key}: {value} -> {jornada[key]}")
                    elif isinstance(value, Decimal):
                        jornada[key] = float(value)
                        print(f"    💰 Convertido {key}: {value} -> {jornada[key]}")
                    elif hasattr(value, 'total_seconds'):  # timedelta
                        jornada[key] = str(value)
                        print(f"    ⏱️ Convertido timedelta {key}: {value} -> {jornada[key]}")
            
            result = {
                'success': True,
                'jornadas': jornadas,
                'empresa_id': empresa_id
            }
            
            print(f"✅ SUCESSO: {len(jornadas)} jornadas encontradas")
            print(f"📄 JSON Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"❌ ERRO: Nenhuma jornada encontrada para empresa {empresa_id}")
            return {
                'success': False,
                'message': f'Nenhuma jornada encontrada para empresa {empresa_id}',
                'jornadas': [],
                'empresa_id': empresa_id
            }

    except Exception as e:
        print(f"❌ EXCEÇÃO: {e}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'message': f'Erro ao buscar jornadas da empresa: {str(e)}',
            'jornadas': [],
            'empresa_id': empresa_id
        }

if __name__ == "__main__":
    # Testar com empresa AiNexus (ID: 11)
    print("=== TESTE API JORNADAS EMPRESA 11 (AiNexus) ===")
    result = test_jornadas_api(11)
    print(f"\n=== RESULTADO FINAL ===")
    print(json.dumps(result, indent=2, ensure_ascii=False))
